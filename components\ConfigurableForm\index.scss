/**
 * 配置化表单组件样式
 * 简洁、现代的表单样式设计
 */

.configurable-form {
  display: flex;
  height: 100%;
  background-color: #f8f9fa;

  /* 左侧导航样式 */
  .form-nav {
    width: 200rpx;
    background-color: #ffffff;
    border-right: 1rpx solid #e9ecef;

    .nav-group {
      border-bottom: 1rpx solid #f1f3f4;

      .nav-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 24rpx 16rpx;
        font-size: 28rpx;
        font-weight: 500;
        color: #333333;
        background-color: #ffffff;
        transition: all 0.3s ease;

        &.active {
          background-color: #e3f2fd;
          color: #1976d2;
          font-weight: 600;
        }

        image {
          width: 24rpx;
          height: 24rpx;
        }
      }

      .nav-fields {
        overflow: hidden;
        transition: height 0.3s ease;

        .nav-field {
          padding: 16rpx 24rpx;
          font-size: 24rpx;
          color: #666666;
          border-bottom: 1rpx solid #f5f5f5;
          transition: all 0.3s ease;

          &.active {
            background-color: #f0f8ff;
            color: #1976d2;
            font-weight: 500;
          }

          &:last-child {
            border-bottom: none;
          }
        }
      }
    }
  }

  /* 右侧表单内容样式 */
  .form-content {
    flex: 1;
    padding: 0 24rpx;

    .form-container {
      .form-item {
        margin-bottom: 32rpx;

        /* 分组标题样式 */
        .group-title {
          padding: 24rpx 0;
          border-bottom: 2rpx solid #e9ecef;
          margin-bottom: 24rpx;

          .title-text {
            font-size: 32rpx;
            font-weight: 600;
            color: #333333;
            position: relative;

            &::before {
              content: '';
              position: absolute;
              left: -16rpx;
              top: 50%;
              transform: translateY(-50%);
              width: 6rpx;
              height: 24rpx;
              background-color: #1976d2;
              border-radius: 3rpx;
            }
          }

          &.active {
            .title-text {
              color: #1976d2;
            }
          }
        }
      }
    }
  }
}

/* 表单字段通用样式 */
.form-field-base {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  }

  .field-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16rpx;

    .field-title {
      display: flex;
      align-items: center;
      gap: 8rpx;

      .title-text {
        font-size: 28rpx;
        font-weight: 500;
        color: #333333;
      }

      .required-mark {
        color: #f44336;
        font-size: 28rpx;
        font-weight: bold;
      }

      .vip-badge {
        background: linear-gradient(135deg, #ffd700, #ffb300);
        color: #ffffff;
        font-size: 20rpx;
        padding: 4rpx 8rpx;
        border-radius: 8rpx;
        font-weight: 500;
      }

      .info-icon {
        width: 32rpx;
        height: 32rpx;
        background-color: #2196f3;
        color: #ffffff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20rpx;
        font-weight: bold;
      }
    }

    .expand-btn {
      padding: 8rpx;

      image {
        width: 24rpx;
        height: 24rpx;
      }
    }
  }

  .error-message {
    margin-top: 8rpx;
    padding: 8rpx 12rpx;
    background-color: #ffebee;
    border-radius: 6rpx;
    border-left: 4rpx solid #f44336;

    .error-text {
      font-size: 24rpx;
      color: #f44336;
    }
  }
}

/* 输入框样式 */
.form-input-container {
  @extend .form-field-base;

  .input-wrapper {
    position: relative;
    display: flex;
    align-items: center;

    .input-field {
      flex: 1;
      height: 80rpx;
      padding: 0 16rpx;
      border: 2rpx solid #e0e0e0;
      border-radius: 8rpx;
      font-size: 28rpx;
      color: #333333;
      transition: border-color 0.3s ease;

      &:focus {
        border-color: #1976d2;
        outline: none;
      }

      &[disabled] {
        background-color: #f5f5f5;
        color: #999999;
      }
    }

    .input-placeholder {
      color: #999999;
    }

    .clear-btn {
      position: absolute;
      right: 16rpx;
      width: 32rpx;
      height: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      image {
        width: 24rpx;
        height: 24rpx;
        opacity: 0.6;
      }
    }
  }

  .search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: #ffffff;
    border: 1rpx solid #e0e0e0;
    border-radius: 8rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-height: 400rpx;

    .suggestions-list {
      max-height: 400rpx;

      .suggestion-item {
        padding: 16rpx;
        border-bottom: 1rpx solid #f0f0f0;
        font-size: 26rpx;
        color: #333333;

        &:last-child {
          border-bottom: none;
        }

        .suggestion-highlight {
          &.highlight {
            background-color: #fff3cd;
            color: #856404;
          }
        }
      }

      .suggestion-hover {
        background-color: #f8f9fa;
      }
    }
  }

  .search-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 32rpx;
    color: #666666;
    font-size: 24rpx;

    .loading-spinner {
      width: 32rpx;
      height: 32rpx;
      border: 3rpx solid #f3f3f3;
      border-top: 3rpx solid #1976d2;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: 16rpx;
    }
  }
}

/* 多选组件样式 */
.form-multi-select {
  @extend .form-field-base;

  .options-container {
    overflow: hidden;
    transition: height 0.3s ease;

    .selected-summary {
      padding: 16rpx;
      background-color: #f8f9fa;
      border-radius: 8rpx;
      border: 1rpx solid #e9ecef;

      .summary-text {
        font-size: 26rpx;
        color: #495057;
      }
    }

    .options-list {
      display: flex;
      flex-wrap: wrap;
      gap: 12rpx;

      .option-item {
        padding: 12rpx 20rpx;
        background-color: #f8f9fa;
        border: 2rpx solid #e9ecef;
        border-radius: 24rpx;
        font-size: 26rpx;
        color: #495057;
        transition: all 0.3s ease;

        &.active {
          background-color: #1976d2;
          border-color: #1976d2;
          color: #ffffff;
        }

        .option-text {
          white-space: nowrap;
        }
      }
    }

    .action-buttons {
      display: flex;
      gap: 16rpx;
      margin-top: 16rpx;

      .action-btn {
        padding: 12rpx 24rpx;
        background-color: #e3f2fd;
        color: #1976d2;
        border-radius: 20rpx;
        font-size: 24rpx;
        text-align: center;
        transition: all 0.3s ease;

        &:active {
          background-color: #bbdefb;
        }
      }
    }
  }

  .selection-stats {
    margin-top: 12rpx;
    font-size: 24rpx;
    color: #666666;

    .stats-limit {
      color: #999999;
    }
  }
}

/* 动画效果 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .configurable-form {
    .form-nav {
      width: 160rpx;
    }
    
    .form-content {
      padding: 0 16rpx;
    }
  }
}
