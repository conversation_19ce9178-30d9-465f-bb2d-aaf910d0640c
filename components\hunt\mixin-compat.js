/**
 * Hunt Mixin 兼容性文件
 * 为了保持向后兼容，重新导出原有的mixin方法
 * 实际功能已迁移到ConfigurableForm组件
 */

// 重新导出原有的mixin内容，保持API兼容
const originalMixin = require('./mixin.js');

// 同时提供新的工具方法
const { clearForm, fillForm, getFormData, validateForm, hasHighlight, debounce, deepClone } = require('../ConfigurableForm/utils.js');

// 兼容性提示
console.warn('⚠️  hunt/mixin.js 已废弃，建议使用新的 ConfigurableForm 组件和 utils.js 工具方法');

// 导出所有原有方法以保持兼容性
module.exports = {
  // 原有的所有导出
  ...originalMixin,
  
  // 新的工具方法（推荐使用）
  clearForm,
  fillForm, 
  getFormData,
  validateForm,
  hasHighlight,
  debounce,
  deepClone
};
