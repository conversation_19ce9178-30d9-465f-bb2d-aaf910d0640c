/**
 * 配置化表单工具方法
 * 优化后的工具方法集合，提供简洁的API
 */

const FormDataProcessor = require('../core/FormDataProcessor.js');
const FormValidator = require('../core/FormValidator.js');

class FormUtils {
  /**
   * 清空表单组件
   * @param {Object} pageContext - 页面上下文
   * @param {string} selector - 组件选择器
   * @returns {Function} 清空函数
   */
  static clearForm(pageContext, selector = '#configurableForm') {
    const component = pageContext.selectComponent(selector);
    
    return function() {
      if (component && component.clearForm) {
        component.clearForm();
      } else {
        console.warn('表单组件未找到或不支持清空操作');
      }
    };
  }

  /**
   * 回填表单数据
   * @param {Object} pageContext - 页面上下文
   * @param {string} selector - 组件选择器
   * @returns {Function} 回填函数
   */
  static fillForm(pageContext, selector = '#configurableForm') {
    const component = pageContext.selectComponent(selector);
    
    return function(data) {
      if (component && component.setFormData) {
        component.setFormData(data);
      } else {
        console.warn('表单组件未找到或不支持回填操作');
      }
    };
  }

  /**
   * 获取表单数据
   * @param {Object} pageContext - 页面上下文
   * @param {string} selector - 组件选择器
   * @returns {Object|null} 表单数据
   */
  static getFormData(pageContext, selector = '#configurableForm') {
    const component = pageContext.selectComponent(selector);
    
    if (component && component.getFormData) {
      return component.getFormData();
    } else {
      console.warn('表单组件未找到或不支持获取数据操作');
      return null;
    }
  }

  /**
   * 验证表单
   * @param {Object} pageContext - 页面上下文
   * @param {string} selector - 组件选择器
   * @returns {Object|null} 验证结果
   */
  static validateForm(pageContext, selector = '#configurableForm') {
    const component = pageContext.selectComponent(selector);
    
    if (component && component.validateForm) {
      return component.validateForm();
    } else {
      console.warn('表单组件未找到或不支持验证操作');
      return null;
    }
  }

  /**
   * 动态添加字段到配置
   * @param {Array} config - 表单配置
   * @param {string} groupKey - 分组key
   * @param {Object} field - 字段配置
   * @returns {Array} 更新后的配置
   */
  static addField(config, groupKey, field) {
    const newConfig = JSON.parse(JSON.stringify(config));
    const group = newConfig.find(g => g.groupKey === groupKey);
    
    if (group) {
      group.fields.push(field);
    } else {
      console.warn(`分组 ${groupKey} 未找到`);
    }
    
    return newConfig;
  }

  /**
   * 从配置中移除字段
   * @param {Array} config - 表单配置
   * @param {string} fieldKey - 字段key
   * @returns {Array} 更新后的配置
   */
  static removeField(config, fieldKey) {
    const newConfig = JSON.parse(JSON.stringify(config));
    
    newConfig.forEach(group => {
      group.fields = group.fields.filter(field => field.key !== fieldKey);
    });
    
    return newConfig.filter(group => group.fields.length > 0);
  }

  /**
   * 更新字段配置
   * @param {Array} config - 表单配置
   * @param {string} fieldKey - 字段key
   * @param {Object} updates - 更新内容
   * @returns {Array} 更新后的配置
   */
  static updateField(config, fieldKey, updates) {
    const newConfig = JSON.parse(JSON.stringify(config));
    
    newConfig.forEach(group => {
      const field = group.fields.find(f => f.key === fieldKey);
      if (field) {
        Object.assign(field, updates);
      }
    });
    
    return newConfig;
  }

  /**
   * 根据组件类型过滤配置
   * @param {Array} config - 表单配置
   * @param {string} componentType - 组件类型
   * @returns {Array} 过滤后的配置
   */
  static filterConfigByType(config, componentType) {
    if (componentType === 'huntCopy') {
      return config.map(group => ({
        ...group,
        fields: group.fields.filter(field => field.key !== 'chain_codes')
      })).filter(group => group.fields.length > 0);
    }
    
    return config;
  }

  /**
   * 生成表单摘要
   * @param {Object} formData - 表单数据
   * @param {Array} config - 表单配置
   * @returns {string} 表单摘要
   */
  static generateSummary(formData, config) {
    return FormDataProcessor.generateSearchSummary(formData, config);
  }

  /**
   * 检查表单是否有高亮状态
   * @param {Object} formData - 表单数据
   * @returns {boolean} 是否有高亮
   */
  static hasHighlight(formData) {
    return FormDataProcessor.getHighlightStatus(formData);
  }

  /**
   * 处理范围输入验证
   * @param {Object} rangeValue - 范围值 {start, end}
   * @param {string} fieldLabel - 字段标签
   * @returns {Object} 验证结果
   */
  static validateRange(rangeValue, fieldLabel) {
    if (!rangeValue || (!rangeValue.start && !rangeValue.end)) {
      return { isValid: true };
    }

    const { start, end } = rangeValue;

    // 数值验证
    if (start && isNaN(Number(start))) {
      return {
        isValid: false,
        message: `${fieldLabel}最小值必须是数字`
      };
    }

    if (end && isNaN(Number(end))) {
      return {
        isValid: false,
        message: `${fieldLabel}最大值必须是数字`
      };
    }

    // 范围验证
    if (start && end && Number(start) >= Number(end)) {
      return {
        isValid: false,
        message: `${fieldLabel}最小值不能大于等于最大值`
      };
    }

    return { isValid: true };
  }

  /**
   * 处理日期范围验证
   * @param {Object} dateRange - 日期范围 {start, end}
   * @param {string} fieldLabel - 字段标签
   * @returns {Object} 验证结果
   */
  static validateDateRange(dateRange, fieldLabel) {
    if (!dateRange || (!dateRange.start && !dateRange.end)) {
      return { isValid: true };
    }

    const { start, end } = dateRange;

    if (start && end) {
      const startTime = new Date(start).getTime();
      const endTime = new Date(end).getTime();
      
      if (startTime >= endTime) {
        return {
          isValid: false,
          message: `${fieldLabel}开始时间不能大于等于结束时间`
        };
      }
    }

    return { isValid: true };
  }

  /**
   * 防抖函数
   * @param {Function} func - 要防抖的函数
   * @param {number} delay - 延迟时间（毫秒）
   * @returns {Function} 防抖后的函数
   */
  static debounce(func, delay = 300) {
    let timeoutId;
    
    return function(...args) {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
  }

  /**
   * 节流函数
   * @param {Function} func - 要节流的函数
   * @param {number} delay - 延迟时间（毫秒）
   * @returns {Function} 节流后的函数
   */
  static throttle(func, delay = 300) {
    let lastTime = 0;
    
    return function(...args) {
      const now = Date.now();
      if (now - lastTime >= delay) {
        lastTime = now;
        func.apply(this, args);
      }
    };
  }

  /**
   * 深拷贝对象
   * @param {*} obj - 要拷贝的对象
   * @returns {*} 拷贝后的对象
   */
  static deepClone(obj) {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }
    
    if (obj instanceof Date) {
      return new Date(obj.getTime());
    }
    
    if (obj instanceof Array) {
      return obj.map(item => this.deepClone(item));
    }
    
    if (typeof obj === 'object') {
      const clonedObj = {};
      Object.keys(obj).forEach(key => {
        clonedObj[key] = this.deepClone(obj[key]);
      });
      return clonedObj;
    }
    
    return obj;
  }

  /**
   * 格式化表单数据用于提交
   * @param {Object} formData - 表单数据
   * @returns {Object} 格式化后的数据
   */
  static formatForSubmit(formData) {
    return FormDataProcessor.getSubmitData(formData);
  }
}

module.exports = FormUtils;
