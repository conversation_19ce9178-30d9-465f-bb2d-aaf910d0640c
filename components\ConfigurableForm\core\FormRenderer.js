/**
 * 表单渲染器
 * 负责将配置转换为渲染数据
 */

class FormRenderer {
  /**
   * 生成渲染列表
   * @param {Array} config - 表单配置
   * @returns {Array} 渲染列表
   */
  static generateRenderList(config) {
    const renderList = [];
    
    config.forEach(group => {
      // 添加分组标题
      renderList.push({
        type: 'groupTitle',
        title: group.groupTitle,
        groupKey: group.groupKey,
        onlyText: true,
        isActive: false
      });

      // 添加字段
      group.fields.forEach(field => {
        const renderItem = this.createRenderItem(field);
        renderList.push(renderItem);
      });
    });

    return renderList;
  }

  /**
   * 生成左侧导航列表
   * @param {Array} config - 表单配置
   * @returns {Array} 导航列表
   */
  static generateLeftNavList(config) {
    return config.map(group => ({
      title: group.groupTitle,
      groupKey: group.groupKey,
      isOpen: true,
      isActive: false,
      fields: group.fields.map(field => ({
        key: field.key,
        label: field.label,
        active: false
      }))
    }));
  }

  /**
   * 创建渲染项
   * @param {Object} field - 字段配置
   * @returns {Object} 渲染项
   */
  static createRenderItem(field) {
    const baseItem = {
      type: field.key,
      title: field.label,
      key: field.key,
      fieldType: field.type,
      vip: field.vip || false,
      icon: field.icon || false,
      special: field.special || field.type,
      content: ''
    };

    // 根据字段类型设置特定属性
    switch (field.type) {
      case 'input':
        return {
          ...baseItem,
          placeholder: field.placeholder || `请输入${field.label}`,
          maxLength: field.maxLength || 50
        };

      case 'select':
      case 'multiSelect':
      case 'radio':
        return {
          ...baseItem,
          list: this.createOptionList(field.options || []),
          isOpenIcon: field.isOpenIcon || false,
          isOpen: field.isOpen !== false // 默认展开
        };

      case 'rangeInput':
        return {
          ...baseItem,
          min: field.minLabel || '最小值',
          max: field.maxLabel || '最大值',
          unit: field.unit || '',
          genre: field.genre || 'input',
          list: this.createOptionList(field.options || [])
        };

      case 'datePop':
        return {
          ...baseItem,
          min: field.minLabel || '开始时间',
          max: field.maxLabel || '结束时间',
          unit: field.unit || '',
          genre: field.genre || 'tpop',
          list: this.createOptionList(field.options || [])
        };

      case 'pop':
        return {
          ...baseItem,
          dataKey: field.dataKey,
          popType: field.popType
        };

      default:
        return baseItem;
    }
  }

  /**
   * 创建选项列表
   * @param {Array} options - 选项配置
   * @returns {Array} 选项列表
   */
  static createOptionList(options) {
    return options.map(option => ({
      id: option.value,
      name: option.label,
      active: false
    }));
  }

  /**
   * 更新渲染项状态
   * @param {Array} renderList - 渲染列表
   * @param {string} fieldKey - 字段key
   * @param {*} value - 字段值
   * @returns {Array} 更新后的渲染列表
   */
  static updateRenderItemState(renderList, fieldKey, value) {
    return renderList.map(item => {
      if (item.key === fieldKey) {
        return this.updateItemByType(item, value);
      }
      return item;
    });
  }

  /**
   * 根据类型更新项目
   * @param {Object} item - 渲染项
   * @param {*} value - 值
   * @returns {Object} 更新后的项目
   */
  static updateItemByType(item, value) {
    switch (item.fieldType) {
      case 'input':
        return { ...item, value };

      case 'select':
      case 'radio':
        return {
          ...item,
          list: item.list.map(option => ({
            ...option,
            active: option.id === value
          }))
        };

      case 'multiSelect':
        const selectedValues = Array.isArray(value) ? value : [];
        return {
          ...item,
          list: item.list.map(option => ({
            ...option,
            active: selectedValues.includes(option.id)
          }))
        };

      case 'pop':
        return {
          ...item,
          content: this.getPopDisplayText(value)
        };

      default:
        return item;
    }
  }

  /**
   * 获取弹窗显示文本
   * @param {*} value - 弹窗值
   * @returns {string} 显示文本
   */
  static getPopDisplayText(value) {
    if (!value || !Array.isArray(value) || value.length === 0) {
      return '全部';
    }

    // 处理复杂的弹窗数据结构
    const selectedItems = value.filter(item => item.status === 'checked');
    if (selectedItems.length === 0) return '全部';

    return selectedItems.map(item => item.name).join('、');
  }

  /**
   * 重置渲染列表状态
   * @param {Array} renderList - 渲染列表
   * @returns {Array} 重置后的列表
   */
  static resetRenderListState(renderList) {
    return renderList.map(item => {
      if (item.list) {
        return {
          ...item,
          list: item.list.map(option => ({
            ...option,
            active: false
          }))
        };
      }
      return {
        ...item,
        value: '',
        content: ''
      };
    });
  }
}

module.exports = FormRenderer;
