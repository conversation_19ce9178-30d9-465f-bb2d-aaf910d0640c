<view style="height:{{wrapHeight}};" class="configurable-form">
  <!-- 左侧导航 -->
  <scroll-view scroll-y style="height:{{wrapHeight}};" scroll-with-animation class="form-nav">
    <view class="nav-group" wx:for="{{leftNavList}}" wx:key="groupKey">
      <view class="nav-title {{activeGroup === item.groupKey ? 'active' : ''}}" 
            bindtap="onLeftNavClick" 
            data-group-key="{{item.groupKey}}">
        <view>{{item.title}}</view>
        <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/h-down.png" 
               mode="aspectFill" 
               wx:if="{{!item.isOpen}}"></image>
        <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/h-upward.png" 
               mode="aspectFill" 
               wx:else></image>
      </view>
      <view class="nav-fields" style="height: {{item.isOpen ? 'auto' : 0}};">
        <view wx:for="{{item.fields}}" 
              wx:for-index="fieldIndex" 
              wx:for-item="field" 
              wx:key="key" 
              class="nav-field {{field.active ? 'active' : ''}}"
              data-field-key="{{field.key}}">
          {{field.label}}
        </view>
      </view>
    </view>
  </scroll-view>

  <!-- 右侧表单内容 -->
  <scroll-view scroll-y style="height:{{wrapHeight}};" scroll-with-animation class="form-content">
    <view class="form-container">
      <view wx:for="{{renderList}}" wx:key="key" class="form-item" data-field="{{item}}">
        
        <!-- 分组标题 -->
        <view wx:if="{{item.type === 'groupTitle'}}" class="group-title {{item.isActive ? 'active' : ''}}">
          <view class="title-text">{{item.title}}</view>
        </view>

        <!-- 输入框类型 -->
        <form-input wx:elif="{{item.fieldType === 'input'}}"
                    field="{{item}}"
                    value="{{formData[item.key]}}"
                    bind:change="onFieldChange">
        </form-input>

        <!-- 单选类型 -->
        <form-radio wx:elif="{{item.fieldType === 'radio'}}"
                    field="{{item}}"
                    value="{{formData[item.key]}}"
                    bind:change="onFieldChange">
        </form-radio>

        <!-- 多选类型 -->
        <form-multi-select wx:elif="{{item.fieldType === 'multiSelect'}}"
                          field="{{item}}"
                          value="{{formData[item.key]}}"
                          bind:change="onFieldChange">
        </form-multi-select>

        <!-- 下拉选择类型 -->
        <form-select wx:elif="{{item.fieldType === 'select'}}"
                     field="{{item}}"
                     value="{{formData[item.key]}}"
                     bind:change="onFieldChange">
        </form-select>

        <!-- 范围输入类型 -->
        <form-range-input wx:elif="{{item.fieldType === 'rangeInput'}}"
                         field="{{item}}"
                         value="{{formData[item.key]}}"
                         bind:change="onFieldChange">
        </form-range-input>

        <!-- 日期选择类型 -->
        <form-date-picker wx:elif="{{item.fieldType === 'datePop'}}"
                         field="{{item}}"
                         value="{{formData[item.key]}}"
                         bind:change="onFieldChange">
        </form-date-picker>

        <!-- 弹窗选择类型 -->
        <form-popup-select wx:elif="{{item.fieldType === 'pop'}}"
                          field="{{item}}"
                          value="{{formData[item.dataKey]}}"
                          bind:change="onPopupSubmit">
        </form-popup-select>

      </view>
    </view>
  </scroll-view>
</view>

<!-- 弹窗组件 -->
<!-- 地区选择弹窗 -->
<multiplec-choice visible="{{popupStates.regionPop}}" 
                  mark="areas" 
                  dataType="districtAry" 
                  bindsubmits="onPopupSubmit" 
                  oldData="{{formData.regionData}}">
</multiplec-choice>

<!-- 行业选择弹窗 -->
<multiplec-choice visible="{{popupStates.eleseicPop}}" 
                  mark="trade_types" 
                  dataType="eleseicAry" 
                  bindsubmits="onPopupSubmit" 
                  oldData="{{formData.eleseic_data}}">
</multiplec-choice>

<!-- 企业类型弹窗 -->
<multiplec-choice visible="{{popupStates.enttypePop}}" 
                  mark="ent_type" 
                  dataType="enttypeAry" 
                  bindsubmits="onPopupSubmit" 
                  oldData="{{formData.enttype_data}}">
</multiplec-choice>

<!-- 企业许可弹窗 -->
<multiplec-choice visible="{{popupStates.districtPop}}" 
                  mark="ent_cert" 
                  dataType="allCertAry" 
                  bindsubmits="onPopupSubmit" 
                  oldData="{{formData.all_cert_data}}">
</multiplec-choice>

<!-- 产业链弹窗 -->
<chainPop visible="{{popupStates.chainCodePop}}" 
          position="bottom" 
          mark="chain_codes" 
          bindsubmits="onPopupSubmit" 
          oldData="{{formData.chain_codes_data}}" />

<!-- 日期选择器 -->
<DatePicker visible="{{popupStates.datePop}}" 
            _date="{{datePickerData.date}}" 
            bindsetDate="onDateSet" 
            changePadding="{{true}}" 
            dateType="{{datePickerData.dateType}}"/>

<!-- VIP弹窗 -->
<vip-popup visible="{{vipVisible}}" bind:close="onVipClose"></vip-popup>
