<view style="height:{{wrapHeight}};" class="searWrap">
  <!-- 左侧导航 -->
  <scroll-view
    scroll-y
    style="height:{{wrapHeight}};"
    scroll-with-animation
    class="searWrap-r"
  >
    <view style="height: auto;">
      <view class="searL" wx:for="{{leftList}}" wx:key="index">
        <view class="{{item.isActive&&'active'}}" wx:if="{{item.onlyText}}">
          <view class="tit" bindtap="closeleft" data-item="{{item}}">
            <view>{{item.title}}</view>
            <image
              src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/h-down.png"
              mode="aspectFill"
              wx:if="{{!item.isOpen}}"
            ></image>
            <image
              src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/h-upward.png"
              mode="aspectFill"
              wx:else
            ></image>
          </view>
          <view class="tit_cont" style="height: {{item.isOpen?'auto':0}};">
            <view
              wx:for="{{item.map}}"
              wx:for-index="key"
              wx:for-item="itm"
              wx:key="key"
              class="{{itm.active&&'child-active'}}"
              bindtap="leftactvie"
              data-item="{{item}}"
              data-itm="{{itm.key}}"
              >{{itm.value}}</view
            >
          </view>
        </view>
      </view>
    </view>
  </scroll-view>

  <!-- 右侧表单内容 -->
  <scroll-view
    scroll-y
    style="height:{{wrapHeight}};"
    scroll-with-animation
    class="searWrap-l"
    scroll-into-view="{{idName}}"
  >
    <view class="content">
      <view
        wx:for="{{itemList}}"
        wx:key="id"
        id="{{item.type}}"
        bindtap="closeInptPop"
      >
        <!-- 输入框 -->
        <view
          class="content-item-pop cnt-ipt"
          wx:if="{{item.special=='input'}}"
          catchtap="_event"
        >
          <view
            class="title"
            style="font-weight: {{idName==item.type?600:400}};"
            >{{item.title}}</view
          >
          <view class="ipt">
            <input
              placeholder="{{item.placeholder || '请输入企业名称(非必填)'}}"
              placeholder-class="placeCls"
              value="{{params.ent_name}}"
              bindfocus="onFocus"
              bindblur="onBlur"
              bindinput="onInput"
              maxlength="{{item.maxLength || 24}}"
            />
          </view>
          <!-- 搜索建议 -->
          <view
            class="child-box"
            wx:if="{{focus&&params.ent_name.length>0}}"
            catchtouchmove="return"
          >
            <scroll-view class="child-box-ul" scroll-y scroll-with-animation>
              <block
                wx:for="{{searchList}}"
                wx:key="index"
                wx:for-item="obj"
                wx:if="{{searchList.length>0}}"
              >
                <view hover-class="hover">
                  <view
                    class="search-li"
                    catchtap="clickItem"
                    data-item="{{obj}}"
                  >
                    <block wx:for="{{obj.ent_name}}" wx:key="index">
                      <text
                        class="listtext {{item==params.ent_name ? 'searchHigh' : '' }}"
                        >{{item}}</text
                      >
                    </block>
                  </view>
                </view>
              </block>
              <block wx:if="{{searchList.length<=0}}">
                <view class="search-li">暂无数据</view>
              </block>
            </scroll-view>
          </view>
        </view>

        <!-- 弹窗选择 -->
        <view
          class="content-item-pop"
          wx:elif="{{item.special=='pop'}}"
          catchtap="_event"
        >
          <view
            class="title"
            style="font-weight: {{idName==item.type?600:400}};"
          >
            {{item.title}}
            <view wx:if="{{item.vip}}" class="vip"></view>
          </view>
          <view
            class="pop"
            bindtap="{{item.type=='areas'?'openRegionPop':item.type=='trade_types'?'openEleseicPop':item.type=='ent_type'?'openEnttypePop':item.type=='ent_cert'?'openDistrictPop':item.type=='chain_codes'?'openChainCodePop':''}}"
          >
            <view class="pop-text">{{item.content || '全部'}}</view>
            <image
              src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/h-right.png"
              mode="aspectFill"
            ></image>
          </view>
        </view>

        <!-- 标签选择 -->
        <view class="content-item" wx:else catchtap="_event">
          <!-- 标题 -->
          <view
            wx:if="{{!item.isOpenIcon}}"
            class="title"
            style="font-weight: {{idName==item.type?600:400}};"
          >
            {{item.title}}
            <view wx:if="{{item.vip}}" class="vip"></view>
            <view
              wx:if="{{item.icon}}"
              class="icon"
              bindtap="showIcon"
              data-item="{{item}}"
              >?</view
            >
          </view>

          <!-- 可展开标题 -->
          <view
            wx:elif="{{item.isOpenIcon}}"
            class="zhankai"
            style="font-weight: {{idName==item.type?600:400}};"
          >
            <view style="display: flex; align-items: center;">
              {{item.title}}
              <view wx:if="{{item.vip}}" class="vip"></view>
            </view>
            <image
              src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/h-upward.png"
              mode="aspectFill"
              wx:if="{{!item.isOpen}}"
              bindtap="openright"
              data-item="{{item}}"
            ></image>
            <image
              src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/h-down.png"
              mode="aspectFill"
              wx:else
              bindtap="openright"
              data-item="{{item}}"
            ></image>
          </view>

          <!-- 内容区域 -->
          <view
            class="wrap"
            style="height: {{item.isOpenIcon&&item.isOpen ? '174rpx':'auto' }};"
          >
            <!-- 标签列表 -->
            <view class="tags" wx:if="{{item.list}}">
              <view
                wx:for="{{item.list}}"
                wx:key="id"
                wx:for-item="tag"
                class="tag {{tag.active?'active':''}}"
                bindtap="selectTag"
                data-id="{{tag.id}}"
                data-type="{{item.type}}"
                data-name="{{tag.name}}"
                data-item="{{item}}"
              >
                {{tag.name}}
              </view>
            </view>

            <!-- 范围输入 -->
            <view wx:if="{{item.genre=='input'}}" class="range-input">
              <view class="input-group">
                <input
                  placeholder="{{item.min}}"
                  value="{{item.type=='register_capital'?minCapital:item.type=='super_dimension_social_num'?socialminPeson:''}}"
                  bindinput="{{item.type=='register_capital'?'capitalInput':'socialInput'}}"
                  data-type="min"
                  type="number"
                />
                <text class="separator">-</text>
                <input
                  placeholder="{{item.max}}"
                  value="{{item.type=='register_capital'?maxCapital:item.type=='super_dimension_social_num'?socialmaxPeson:''}}"
                  bindinput="{{item.type=='register_capital'?'capitalInput':'socialInput'}}"
                  data-type="max"
                  type="number"
                />
                <text class="unit">{{item.unit}}</text>
              </view>
              <view
                class="active-indicator"
                wx:if="{{item.type=='register_capital'?capitalActive:item.type=='super_dimension_social_num'?socialActive:false}}"
                >已设置范围</view
              >
            </view>

            <!-- 日期选择 -->
            <view wx:if="{{item.genre=='tpop'}}" class="date-input">
              <view class="input-group">
                <view class="date-btn" bindtap="openDatePop" data-type="min">
                  <text>{{minDate || item.min}}</text>
                </view>
                <text class="separator">-</text>
                <view class="date-btn" bindtap="openDatePop" data-type="max">
                  <text>{{maxDate || item.max}}</text>
                </view>
                <text class="unit">{{item.unit}}</text>
              </view>
              <view class="active-indicator" wx:if="{{dateActive}}"
                >已设置时间范围</view
              >
            </view>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>
</view>

<!-- 弹窗组件 -->
<multiplec-choice
  visible="{{regionPop}}"
  mark="areas"
  dataType="districtAry"
  bindsubmits="submits"
  oldData="{{params.regionData}}"
></multiplec-choice>
<multiplec-choice
  visible="{{eleseicPop}}"
  mark="trade_types"
  dataType="eleseicAry"
  bindsubmits="submits"
  oldData="{{params.eleseic_data}}"
></multiplec-choice>
<multiplec-choice
  visible="{{enttypePop}}"
  mark="ent_type"
  dataType="enttypeAry"
  bindsubmits="submits"
  oldData="{{params.enttype_data}}"
></multiplec-choice>
<multiplec-choice
  visible="{{districtPop}}"
  mark="ent_cert"
  dataType="allCertAry"
  bindsubmits="submits"
  oldData="{{params.all_cert_data}}"
></multiplec-choice>
<chainPop
  wx:if="{{componentType === 'hunt'}}"
  visible="{{chainCodePop}}"
  position="bottom"
  mark="chain_codes"
  bindsubmits="submits"
  oldData="{{params.chain_codes_data}}"
/>
<DatePicker
  visible="{{datePop}}"
  _date="{{date}}"
  bindsetDate="setDate"
  changePadding="{{true}}"
  dateType="{{dateType}}"
/>

<!-- VIP弹窗 -->
<half-screen-pop visible="{{vipVisible}}" bindclose="vipClose">
  <view slot="content" class="vip-content">
    <view class="vip-title">VIP功能</view>
    <view class="vip-desc">此功能需要VIP权限才能使用</view>
  </view>
</half-screen-pop>
