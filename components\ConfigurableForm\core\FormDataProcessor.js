/**
 * 表单数据处理器
 * 负责表单数据的处理、转换和验证
 */

class FormDataProcessor {
  /**
   * 更新字段值
   * @param {Object} formData - 表单数据
   * @param {Object} field - 字段配置
   * @param {*} value - 新值
   * @returns {Object} 更新后的表单数据
   */
  static updateFieldValue(formData, field, value) {
    const newFormData = { ...formData };
    
    switch (field.type) {
      case 'input':
        newFormData[field.key] = value;
        break;
        
      case 'radio':
      case 'select':
        newFormData[field.key] = [value];
        break;
        
      case 'multiSelect':
        newFormData[field.key] = Array.isArray(value) ? value : [value];
        break;
        
      case 'rangeInput':
      case 'datePop':
        newFormData[field.key] = this.processRangeValue(value);
        break;
        
      case 'pop':
        newFormData[field.dataKey] = value;
        break;
        
      default:
        newFormData[field.key] = value;
    }
    
    return newFormData;
  }

  /**
   * 处理标签选择
   * @param {Object} formData - 表单数据
   * @param {Object} field - 字段配置
   * @param {Object} option - 选项
   * @param {boolean} isMultiple - 是否多选
   * @returns {Object} 更新后的表单数据
   */
  static handleTagSelect(formData, field, option, isMultiple) {
    const newFormData = { ...formData };
    const currentValue = newFormData[field.key] || [];
    
    if (isMultiple) {
      // 多选逻辑
      const index = currentValue.indexOf(option.value);
      if (index > -1) {
        currentValue.splice(index, 1);
      } else {
        currentValue.push(option.value);
      }
      newFormData[field.key] = [...currentValue];
    } else {
      // 单选逻辑
      newFormData[field.key] = currentValue.includes(option.value) ? [] : [option.value];
    }
    
    return newFormData;
  }

  /**
   * 处理弹窗数据
   * @param {Object} formData - 表单数据
   * @param {Object} field - 字段配置
   * @param {*} data - 弹窗数据
   * @returns {Object} 更新后的表单数据
   */
  static handlePopupData(formData, field, data) {
    const newFormData = { ...formData };
    
    if (field.dataKey) {
      newFormData[field.dataKey] = data;
    } else {
      newFormData[field.key] = data;
    }
    
    return newFormData;
  }

  /**
   * 处理范围值
   * @param {Object} value - 范围值 {start, end}
   * @returns {Array} 处理后的数组
   */
  static processRangeValue(value) {
    if (!value || (!value.start && !value.end)) {
      return [];
    }
    
    return [{
      start: value.start || '',
      end: value.end || '',
      special: true
    }];
  }

  /**
   * 处理回填数据
   * @param {Object} data - 回填数据
   * @returns {Object} 处理后的数据
   */
  static processBackfillData(data) {
    const processedData = {};
    
    Object.keys(data).forEach(key => {
      const value = data[key];
      
      // 处理不同类型的回填数据
      if (Array.isArray(value)) {
        if (value.length > 0 && typeof value[0] === 'object' && value[0].start !== undefined) {
          // 范围类型数据
          processedData[key] = value;
        } else {
          // 普通数组数据
          processedData[key] = value;
        }
      } else {
        // 字符串或其他类型
        processedData[key] = value;
      }
    });
    
    return processedData;
  }

  /**
   * 获取提交数据
   * @param {Object} formData - 表单数据
   * @returns {Object} 提交数据
   */
  static getSubmitData(formData) {
    const submitData = {};
    
    Object.keys(formData).forEach(key => {
      const value = formData[key];
      
      // 过滤空值
      if (this.isValidValue(value)) {
        submitData[key] = this.processSubmitValue(value);
      }
    });
    
    return submitData;
  }

  /**
   * 检查值是否有效
   * @param {*} value - 值
   * @returns {boolean} 是否有效
   */
  static isValidValue(value) {
    if (value === null || value === undefined || value === '') {
      return false;
    }
    
    if (Array.isArray(value)) {
      return value.length > 0;
    }
    
    return true;
  }

  /**
   * 处理提交值
   * @param {*} value - 原始值
   * @returns {*} 处理后的值
   */
  static processSubmitValue(value) {
    // 处理布尔值字符串
    if (Array.isArray(value) && value.length === 1) {
      const singleValue = value[0];
      if (singleValue === 'true' || singleValue === 'false') {
        return JSON.parse(singleValue);
      }
    }
    
    return value;
  }

  /**
   * 获取高亮状态
   * @param {Object} formData - 表单数据
   * @returns {boolean} 是否有高亮
   */
  static getHighlightStatus(formData) {
    return Object.keys(formData).some(key => {
      const value = formData[key];
      
      if (typeof value === 'string') {
        return value.trim().length > 0;
      }
      
      if (Array.isArray(value)) {
        if (value.length === 0) return false;
        
        // 检查范围类型数据
        if (value[0] && typeof value[0] === 'object' && value[0].start !== undefined) {
          return value[0].start || value[0].end;
        }
        
        return true;
      }
      
      return false;
    });
  }

  /**
   * 处理多选地区数据（去除父子重复）
   * @param {Array} data - 地区数据
   * @returns {Array} 处理后的数据
   */
  static handleMultipleRegionData(data = []) {
    if (!data.length) return [];
    
    const tempData = JSON.parse(JSON.stringify(data));
    const checkedData = tempData.filter(item => item.status === 'checked' && !item.ischildren);
    
    // 过滤掉父级已选中的子级
    const filteredData = checkedData.filter(item => 
      !checkedData.map(parent => parent.code).includes(item.parent)
    );
    
    if (!filteredData.length) return data;
    
    // 移除已选父级的子级
    filteredData.forEach(parent => {
      tempData.splice(
        tempData.findIndex(child => parent.code === child.parent),
        1
      );
    });
    
    return tempData;
  }

  /**
   * 生成搜索摘要
   * @param {Object} formData - 表单数据
   * @param {Array} config - 表单配置
   * @returns {string} 搜索摘要
   */
  static generateSearchSummary(formData, config) {
    const summaryParts = [];
    
    config.forEach(group => {
      group.fields.forEach(field => {
        const value = formData[field.key] || formData[field.dataKey];
        if (this.isValidValue(value)) {
          const displayText = this.getFieldDisplayText(field, value);
          if (displayText) {
            summaryParts.push(`${field.label}: ${displayText}`);
          }
        }
      });
    });
    
    return summaryParts.join('; ');
  }

  /**
   * 获取字段显示文本
   * @param {Object} field - 字段配置
   * @param {*} value - 字段值
   * @returns {string} 显示文本
   */
  static getFieldDisplayText(field, value) {
    if (field.type === 'pop') {
      return this.getPopDisplayText(value);
    }
    
    if (Array.isArray(value)) {
      if (value.length === 1 && typeof value[0] === 'object' && value[0].start !== undefined) {
        // 范围类型
        const range = value[0];
        return `${range.start || '不限'} - ${range.end || '不限'}`;
      }
      
      // 选项类型
      return value.map(v => {
        const option = field.options?.find(opt => opt.value === v);
        return option ? option.label : v;
      }).join('、');
    }
    
    return String(value);
  }

  /**
   * 获取弹窗显示文本
   * @param {Array} data - 弹窗数据
   * @returns {string} 显示文本
   */
  static getPopDisplayText(data) {
    if (!Array.isArray(data) || data.length === 0) {
      return '全部';
    }
    
    const selectedItems = data.filter(item => item.status === 'checked');
    if (selectedItems.length === 0) return '全部';
    
    return selectedItems.map(item => item.name).join('、');
  }
}

module.exports = FormDataProcessor;
