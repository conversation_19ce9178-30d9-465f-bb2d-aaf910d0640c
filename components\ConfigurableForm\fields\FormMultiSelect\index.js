/**
 * 表单多选组件
 * 可复用的多选字段组件
 */

Component({
  properties: {
    // 字段配置
    field: {
      type: Object,
      value: {}
    },
    // 字段值（数组）
    value: {
      type: Array,
      value: []
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    }
  },

  data: {
    isExpanded: true, // 是否展开
    selectedCount: 0  // 选中数量
  },

  observers: {
    'value': function(newValue) {
      this.setData({
        selectedCount: Array.isArray(newValue) ? newValue.length : 0
      });
      this.updateOptionsState();
    },
    'field.isOpen': function(isOpen) {
      this.setData({
        isExpanded: isOpen !== false
      });
    }
  },

  lifetimes: {
    attached() {
      this.setData({
        isExpanded: this.data.field.isOpen !== false,
        selectedCount: Array.isArray(this.data.value) ? this.data.value.length : 0
      });
      this.updateOptionsState();
    }
  },

  methods: {
    /**
     * 切换展开/收起状态
     */
    toggleExpand() {
      if (!this.data.field.isOpenIcon) return;
      
      this.setData({
        isExpanded: !this.data.isExpanded
      });
    },

    /**
     * 选择选项
     */
    onOptionSelect(event) {
      const { option } = event.currentTarget.dataset;
      const currentValue = this.data.value || [];
      let newValue = [...currentValue];

      // 检查是否已选中
      const index = newValue.indexOf(option.id);
      
      if (index > -1) {
        // 已选中，取消选择
        newValue.splice(index, 1);
      } else {
        // 未选中，添加选择
        // 检查最大选择数量限制
        if (this.data.field.maxSelect && newValue.length >= this.data.field.maxSelect) {
          wx.showToast({
            title: `最多只能选择${this.data.field.maxSelect}项`,
            icon: 'none'
          });
          return;
        }
        newValue.push(option.id);
      }

      // 触发变化事件
      this.triggerEvent('change', {
        field: this.data.field,
        value: newValue
      });
    },

    /**
     * 更新选项状态
     */
    updateOptionsState() {
      const currentValue = this.data.value || [];
      const field = this.data.field;
      
      if (!field.options) return;

      const updatedOptions = field.options.map(option => ({
        ...option,
        active: currentValue.includes(option.value)
      }));

      // 更新字段配置中的选项状态
      this.setData({
        [`field.options`]: updatedOptions
      });
    },

    /**
     * 全选
     */
    selectAll() {
      if (!this.data.field.options) return;
      
      const allValues = this.data.field.options.map(option => option.value);
      
      this.triggerEvent('change', {
        field: this.data.field,
        value: allValues
      });
    },

    /**
     * 清空选择
     */
    clearAll() {
      this.triggerEvent('change', {
        field: this.data.field,
        value: []
      });
    },

    /**
     * 获取选中项的显示文本
     */
    getSelectedText() {
      const currentValue = this.data.value || [];
      const field = this.data.field;
      
      if (!field.options || currentValue.length === 0) {
        return '请选择';
      }

      const selectedOptions = field.options.filter(option => 
        currentValue.includes(option.value)
      );

      if (selectedOptions.length <= 2) {
        return selectedOptions.map(option => option.label).join('、');
      } else {
        return `已选择${selectedOptions.length}项`;
      }
    },

    /**
     * 检查VIP权限
     */
    async checkVipPermission() {
      if (!this.data.field.vip) return true;
      
      // 这里应该调用实际的权限检查逻辑
      // 暂时返回true
      return true;
    }
  }
});
