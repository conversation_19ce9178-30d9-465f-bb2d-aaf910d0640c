/**
 * 表单验证器
 * 负责表单数据的验证逻辑
 */

class FormValidator {
  /**
   * 验证表单数据
   * @param {Object} formData - 表单数据
   * @param {Array} config - 表单配置
   * @returns {Object} 验证结果 {isValid, errors}
   */
  static validate(formData, config) {
    const errors = [];
    let isValid = true;

    config.forEach(group => {
      group.fields.forEach(field => {
        const fieldErrors = this.validateField(field, formData);
        if (fieldErrors.length > 0) {
          isValid = false;
          errors.push(...fieldErrors);
        }
      });
    });

    return { isValid, errors };
  }

  /**
   * 验证单个字段
   * @param {Object} field - 字段配置
   * @param {Object} formData - 表单数据
   * @returns {Array} 错误信息数组
   */
  static validateField(field, formData) {
    const errors = [];
    const value = formData[field.key] || formData[field.dataKey];

    // 必填验证
    if (field.required && !this.hasValue(value)) {
      errors.push({
        field: field.key,
        message: `${field.label}不能为空`
      });
      return errors;
    }

    // 如果没有值，跳过其他验证
    if (!this.hasValue(value)) {
      return errors;
    }

    // 根据字段类型进行特定验证
    switch (field.type) {
      case 'input':
        errors.push(...this.validateInput(field, value));
        break;
      case 'rangeInput':
        errors.push(...this.validateRangeInput(field, value));
        break;
      case 'datePop':
        errors.push(...this.validateDateRange(field, value));
        break;
      case 'multiSelect':
        errors.push(...this.validateMultiSelect(field, value));
        break;
    }

    return errors;
  }

  /**
   * 检查是否有值
   * @param {*} value - 值
   * @returns {boolean} 是否有值
   */
  static hasValue(value) {
    if (value === null || value === undefined || value === '') {
      return false;
    }
    
    if (Array.isArray(value)) {
      return value.length > 0;
    }
    
    return true;
  }

  /**
   * 验证输入框
   * @param {Object} field - 字段配置
   * @param {*} value - 值
   * @returns {Array} 错误信息
   */
  static validateInput(field, value) {
    const errors = [];
    const strValue = String(value);

    // 长度验证
    if (field.maxLength && strValue.length > field.maxLength) {
      errors.push({
        field: field.key,
        message: `${field.label}长度不能超过${field.maxLength}个字符`
      });
    }

    if (field.minLength && strValue.length < field.minLength) {
      errors.push({
        field: field.key,
        message: `${field.label}长度不能少于${field.minLength}个字符`
      });
    }

    // 格式验证
    if (field.pattern && !new RegExp(field.pattern).test(strValue)) {
      errors.push({
        field: field.key,
        message: field.patternMessage || `${field.label}格式不正确`
      });
    }

    return errors;
  }

  /**
   * 验证范围输入
   * @param {Object} field - 字段配置
   * @param {Array} value - 范围值
   * @returns {Array} 错误信息
   */
  static validateRangeInput(field, value) {
    const errors = [];
    
    if (!Array.isArray(value) || value.length === 0) {
      return errors;
    }

    const range = value[0];
    if (!range || typeof range !== 'object') {
      return errors;
    }

    const { start, end } = range;

    // 数值验证
    if (start && isNaN(Number(start))) {
      errors.push({
        field: field.key,
        message: `${field.minLabel || '最小值'}必须是数字`
      });
    }

    if (end && isNaN(Number(end))) {
      errors.push({
        field: field.key,
        message: `${field.maxLabel || '最大值'}必须是数字`
      });
    }

    // 范围验证
    if (start && end && Number(start) >= Number(end)) {
      errors.push({
        field: field.key,
        message: `${field.minLabel || '最小值'}不能大于等于${field.maxLabel || '最大值'}`
      });
    }

    return errors;
  }

  /**
   * 验证日期范围
   * @param {Object} field - 字段配置
   * @param {Array} value - 日期范围值
   * @returns {Array} 错误信息
   */
  static validateDateRange(field, value) {
    const errors = [];
    
    if (!Array.isArray(value) || value.length === 0) {
      return errors;
    }

    const range = value[0];
    if (!range || typeof range !== 'object') {
      return errors;
    }

    const { start, end } = range;

    // 日期格式验证
    if (start && !this.isValidDate(start)) {
      errors.push({
        field: field.key,
        message: `${field.minLabel || '开始时间'}格式不正确`
      });
    }

    if (end && !this.isValidDate(end)) {
      errors.push({
        field: field.key,
        message: `${field.maxLabel || '结束时间'}格式不正确`
      });
    }

    // 日期范围验证
    if (start && end) {
      const startTime = new Date(start).getTime();
      const endTime = new Date(end).getTime();
      
      if (startTime >= endTime) {
        errors.push({
          field: field.key,
          message: `${field.minLabel || '开始时间'}不能大于等于${field.maxLabel || '结束时间'}`
        });
      }
    }

    return errors;
  }

  /**
   * 验证多选字段
   * @param {Object} field - 字段配置
   * @param {Array} value - 多选值
   * @returns {Array} 错误信息
   */
  static validateMultiSelect(field, value) {
    const errors = [];
    
    if (!Array.isArray(value)) {
      return errors;
    }

    // 最大选择数量验证
    if (field.maxSelect && value.length > field.maxSelect) {
      errors.push({
        field: field.key,
        message: `${field.label}最多只能选择${field.maxSelect}项`
      });
    }

    // 最小选择数量验证
    if (field.minSelect && value.length < field.minSelect) {
      errors.push({
        field: field.key,
        message: `${field.label}至少需要选择${field.minSelect}项`
      });
    }

    return errors;
  }

  /**
   * 验证日期格式
   * @param {string} dateString - 日期字符串
   * @returns {boolean} 是否有效
   */
  static isValidDate(dateString) {
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date);
  }

  /**
   * 验证必填字段
   * @param {Object} formData - 表单数据
   * @param {Array} requiredFields - 必填字段列表
   * @returns {Object} 验证结果
   */
  static validateRequired(formData, requiredFields = []) {
    const errors = [];
    let isValid = true;

    requiredFields.forEach(fieldKey => {
      const value = formData[fieldKey];
      if (!this.hasValue(value)) {
        isValid = false;
        errors.push({
          field: fieldKey,
          message: `${fieldKey}不能为空`
        });
      }
    });

    return { isValid, errors };
  }

  /**
   * 自定义验证规则
   * @param {Object} formData - 表单数据
   * @param {Array} customRules - 自定义规则数组
   * @returns {Object} 验证结果
   */
  static validateCustomRules(formData, customRules = []) {
    const errors = [];
    let isValid = true;

    customRules.forEach(rule => {
      try {
        const result = rule.validator(formData);
        if (!result.isValid) {
          isValid = false;
          errors.push({
            field: rule.field || 'custom',
            message: result.message || '验证失败'
          });
        }
      } catch (error) {
        console.error('自定义验证规则执行失败:', error);
        isValid = false;
        errors.push({
          field: rule.field || 'custom',
          message: '验证规则执行失败'
        });
      }
    });

    return { isValid, errors };
  }
}

module.exports = FormValidator;
