/**
 * 配置化表单工具方法 - 简化版本
 * 提供给外部页面使用的工具函数
 */

/**
 * 清空表单组件
 * @param {Object} pageContext - 页面上下文
 * @param {string} selector - 组件选择器
 * @returns {Function} 清空函数
 */
function clearForm(pageContext, selector = '#searchForm') {
  const component = pageContext.selectComponent(selector);
  
  return function() {
    if (component && component.clearForm) {
      component.clearForm();
    } else {
      console.warn('表单组件未找到或不支持清空操作');
    }
  };
}

/**
 * 回填表单数据
 * @param {Object} pageContext - 页面上下文
 * @param {string} selector - 组件选择器
 * @returns {Function} 回填函数
 */
function fillForm(pageContext, selector = '#searchForm') {
  const component = pageContext.selectComponent(selector);
  
  return function(data) {
    if (component && component.setFormData) {
      component.setFormData(data);
    } else {
      console.warn('表单组件未找到或不支持回填操作');
    }
  };
}

/**
 * 获取表单数据
 * @param {Object} pageContext - 页面上下文
 * @param {string} selector - 组件选择器
 * @returns {Object|null} 表单数据
 */
function getFormData(pageContext, selector = '#searchForm') {
  const component = pageContext.selectComponent(selector);
  
  if (component && component.getFormData) {
    return component.getFormData();
  } else {
    console.warn('表单组件未找到或不支持获取数据操作');
    return null;
  }
}

/**
 * 验证表单
 * @param {Object} pageContext - 页面上下文
 * @param {string} selector - 组件选择器
 * @returns {Object|null} 验证结果
 */
function validateForm(pageContext, selector = '#searchForm') {
  const component = pageContext.selectComponent(selector);
  
  if (component && component.validateForm) {
    return component.validateForm();
  } else {
    console.warn('表单组件未找到或不支持验证操作');
    return null;
  }
}

/**
 * 检查表单是否有高亮状态
 * @param {Object} formData - 表单数据
 * @returns {boolean} 是否有高亮
 */
function hasHighlight(formData) {
  return Object.keys(formData).some(key => {
    const value = formData[key];
    if (typeof value === 'string') return value.trim().length > 0;
    if (Array.isArray(value)) {
      if (value.length === 0) return false;
      if (value[0] && typeof value[0] === 'object' && value[0].start !== undefined) {
        return value[0].start || value[0].end;
      }
      return true;
    }
    return false;
  });
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
function debounce(func, delay = 300) {
  let timeoutId;
  
  return function(...args) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(this, args), delay);
  };
}

/**
 * 深拷贝对象
 * @param {*} obj - 要拷贝的对象
 * @returns {*} 拷贝后的对象
 */
function deepClone(obj) {
  return JSON.parse(JSON.stringify(obj));
}

/**
 * 兼容原有API的方法
 */

// 兼容原有的clearChildComponent方法
const clearChildComponent = clearForm;

// 兼容原有的fillChildComponent方法  
const fillChildComponent = fillForm;

// 兼容原有的checkoutSear方法
function checkoutSear(data) {
  return hasHighlight(data);
}

// 兼容原有的handleData方法
function handleData(data) {
  // 这里可以添加数据处理逻辑
  return data;
}

module.exports = {
  // 新的API
  clearForm,
  fillForm,
  getFormData,
  validateForm,
  hasHighlight,
  debounce,
  deepClone,
  
  // 兼容原有API
  clearChildComponent,
  fillChildComponent,
  checkoutSear,
  handleData
};
