# 迁移指南：从 Hunt 组件到 ConfigurableForm

## 概述

本指南将帮助你从原有的 hunt/huntCopy 组件迁移到新的配置化表单组件。新组件保持了完全的向后兼容性，同时提供了更简洁的API和更好的可维护性。

## 迁移步骤

### 第一步：更新组件引用

#### 原有方式
```json
// page.json
{
  "usingComponents": {
    "hunt": "/components/hunt/index",
    "huntCopy": "/components/huntCopy/index"
  }
}
```

#### 新方式
```json
// page.json
{
  "usingComponents": {
    "configurable-form": "/components/ConfigurableForm/index"
  }
}
```

### 第二步：更新WXML模板

#### 原有方式
```xml
<!-- hunt组件 -->
<hunt
  id="hunt"
  bind:submit="onSearchSubmit"
  bind:vip="onVipShow"
  wrapHeight="calc(100vh - 200rpx)"
  isPage="{{true}}"
/>

<!-- huntCopy组件 -->
<huntCopy
  id="huntCopy"
  bind:submit="onSearchSubmit"
  bind:vip="onVipShow"
  wrapHeight="calc(100vh - 200rpx)"
/>
```

#### 新方式
```xml
<!-- 配置化表单 -->
<configurable-form
  id="configurableForm"
  config="{{formConfig}}"
  componentType="hunt"
  bind:change="onFormChange"
  bind:vip="onVipShow"
  wrapHeight="calc(100vh - 200rpx)"
  isPage="{{true}}"
/>
```

### 第三步：更新JS逻辑

#### 原有方式
```javascript
import {
  clearChildComponent,
  fillChildComponent,
  handleData,
  checkoutSear
} from '/components/hunt/mixin';

Page({
  onLoad() {
    this.clearSearch = clearChildComponent(this, '#hunt');
    this.fillSearch = fillChildComponent(this, '#hunt');
  },

  onSearchSubmit(e) {
    const { isHeight, paramsData } = e.detail;
    if (checkoutSear(paramsData)) {
      this.performSearch(paramsData);
    }
  },

  clearForm() {
    this.clearSearch();
  },

  fillForm(data) {
    this.fillSearch(data);
  }
});
```

#### 新方式
```javascript
const FormUtils = require('/components/ConfigurableForm/utils/FormUtils.js');
const { FORM_CONFIG } = require('/components/hunt/common/config.js');

Page({
  data: {
    formConfig: FORM_CONFIG
  },

  onLoad() {
    this.clearForm = FormUtils.clearForm(this, '#configurableForm');
    this.fillForm = FormUtils.fillForm(this, '#configurableForm');
  },

  onFormChange(event) {
    const { formData, isValid, errors } = event.detail;
    
    if (isValid && FormUtils.hasHighlight(formData)) {
      const submitData = FormUtils.formatForSubmit(formData);
      this.performSearch(submitData);
    }
  },

  clearForm() {
    this.clearForm();
  },

  fillForm(data) {
    this.fillForm(data);
  }
});
```

## API对照表

### 组件属性

| 原有属性 | 新属性 | 说明 |
|---------|--------|------|
| `isPage` | `isPage` | 保持不变 |
| `wrapHeight` | `wrapHeight` | 保持不变 |
| - | `config` | 新增：表单配置数组 |
| - | `componentType` | 新增：组件类型(hunt/huntCopy) |

### 事件对照

| 原有事件 | 新事件 | 数据格式变化 |
|---------|--------|-------------|
| `bind:submit` | `bind:change` | `{isHeight, paramsData}` → `{formData, isValid, errors}` |
| `bind:vip` | `bind:vip` | 保持不变 |

### 方法对照

| 原有方法 | 新方法 | 使用方式 |
|---------|--------|----------|
| `clearChildComponent(this, '#hunt')()` | `FormUtils.clearForm(this)()` | 更简洁的API |
| `fillChildComponent(this, '#hunt')(data)` | `FormUtils.fillForm(this)(data)` | 更简洁的API |
| `checkoutSear(data)` | `FormUtils.validateForm(this).isValid` | 更完善的验证 |
| `handleData(data)` | `FormUtils.formatForSubmit(data)` | 更清晰的命名 |

## 渐进式迁移

### 阶段一：最小改动迁移

如果你想快速迁移而不改变太多代码，可以创建一个兼容层：

```javascript
// compatibility.js - 兼容层
const FormUtils = require('/components/ConfigurableForm/utils/FormUtils.js');

// 保持原有API不变
export const clearChildComponent = (pageContext, selector = '#configurableForm') => {
  return FormUtils.clearForm(pageContext, selector);
};

export const fillChildComponent = (pageContext, selector = '#configurableForm') => {
  return FormUtils.fillForm(pageContext, selector);
};

export const checkoutSear = (data) => {
  // 简化的验证逻辑，保持原有行为
  return FormUtils.hasHighlight(data);
};

export const handleData = (data) => {
  return FormUtils.formatForSubmit(data);
};
```

### 阶段二：逐步优化

逐步替换为新的API：

```javascript
// 第一步：替换工具方法
const FormUtils = require('/components/ConfigurableForm/utils/FormUtils.js');

// 第二步：使用新的事件处理
onFormChange(event) {
  const { formData, isValid } = event.detail;
  
  // 保持原有的数据处理逻辑
  if (isValid) {
    const submitData = FormUtils.formatForSubmit(formData);
    this.performSearch(submitData);
  }
}

// 第三步：优化验证逻辑
validateAndSearch() {
  const validation = FormUtils.validateForm(this);
  if (validation.isValid) {
    const formData = FormUtils.getFormData(this);
    this.performSearch(formData);
  } else {
    this.showValidationErrors(validation.errors);
  }
}
```

### 阶段三：完全迁移

使用所有新特性：

```javascript
const FormUtils = require('/components/ConfigurableForm/utils/FormUtils.js');

Page({
  data: {
    // 使用配置化的表单定义
    formConfig: [
      {
        groupTitle: '基础筛选',
        groupKey: 'basic',
        fields: [
          {
            type: 'input',
            label: '企业名称',
            key: 'ent_name',
            required: true,
            placeholder: '请输入企业名称'
          }
        ]
      }
    ]
  },

  onLoad() {
    // 动态配置表单
    this.customizeForm();
  },

  customizeForm() {
    let config = [...this.data.formConfig];
    
    // 根据用户权限动态添加字段
    if (this.hasVipAccess()) {
      config = FormUtils.addField(config, 'basic', {
        type: 'multiSelect',
        label: '融资信息',
        key: 'financing_info',
        vip: true,
        options: [
          { value: 'A轮', label: 'A轮' },
          { value: 'B轮', label: 'B轮' }
        ]
      });
    }
    
    this.setData({ formConfig: config });
  },

  onFormChange: FormUtils.debounce(function(event) {
    const { formData, isValid } = event.detail;
    
    if (isValid && FormUtils.hasHighlight(formData)) {
      this.performSearch(formData);
    }
  }, 300)
});
```

## 常见问题

### Q1: 原有的数据格式是否兼容？
A: 完全兼容。新组件的数据输出格式与原有组件保持一致。

### Q2: 性能是否有影响？
A: 性能有显著提升。新组件采用了更优化的渲染策略和数据处理方式。

### Q3: 样式是否需要调整？
A: 大部分样式保持兼容。新组件提供了更现代的默认样式，如需自定义可以覆盖CSS类。

### Q4: 如何处理自定义字段？
A: 新组件支持动态添加字段，比原有方式更灵活：

```javascript
// 动态添加自定义字段
let config = FormUtils.addField(this.data.formConfig, 'basic', {
  type: 'input',
  label: '自定义字段',
  key: 'custom_field',
  placeholder: '请输入内容'
});

this.setData({ formConfig: config });
```

### Q5: 如何保持原有的VIP逻辑？
A: VIP逻辑完全保持不变，只需要在字段配置中添加 `vip: true` 属性。

## 迁移检查清单

- [ ] 更新组件引用 (JSON配置)
- [ ] 更新WXML模板
- [ ] 更新JS事件处理
- [ ] 替换工具方法调用
- [ ] 测试表单功能
- [ ] 测试数据回填
- [ ] 测试VIP权限
- [ ] 测试样式显示
- [ ] 性能测试
- [ ] 用户体验测试

## 回滚方案

如果迁移过程中遇到问题，可以快速回滚：

1. 恢复原有的组件引用
2. 恢复原有的WXML模板
3. 恢复原有的JS逻辑

新组件与原有组件可以并存，支持逐步迁移。

## 技术支持

如果在迁移过程中遇到问题，可以：

1. 查看 `components/ConfigurableForm/example.js` 中的完整示例
2. 参考 `components/ConfigurableForm/README.md` 中的详细文档
3. 使用兼容层进行过渡迁移
