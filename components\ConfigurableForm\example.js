/**
 * 配置化表单使用示例
 * 展示如何使用新的配置化表单组件
 */

const FormUtils = require('./utils/FormUtils.js');
const { FORM_CONFIG } = require('../hunt/common/config.js');

/**
 * 页面使用示例
 */
Page({
  data: {
    // 表单配置（可以动态修改）
    formConfig: FORM_CONFIG,
    // 搜索结果
    searchResults: [],
    // 加载状态
    loading: false,
    // 表单数据
    currentFormData: {}
  },

  onLoad() {
    // 初始化表单工具函数
    this.clearForm = FormUtils.clearForm(this, '#configurableForm');
    this.fillForm = FormUtils.fillForm(this, '#configurableForm');
    
    // 可以动态修改配置
    this.customizeFormConfig();
  },

  /**
   * 自定义表单配置示例
   */
  customizeFormConfig() {
    let config = [...this.data.formConfig];
    
    // 添加自定义字段
    config = FormUtils.addField(config, 'basic', {
      type: 'input',
      label: '统一社会信用代码',
      key: 'credit_code',
      placeholder: '请输入统一社会信用代码',
      maxLength: 18,
      pattern: '^[0-9A-HJ-NPQRTUWXY]{2}\\d{6}[0-9A-HJ-NPQRTUWXY]{10}$',
      patternMessage: '请输入正确的统一社会信用代码格式'
    });

    // 更新字段配置
    config = FormUtils.updateField(config, 'ent_name', {
      required: true,
      placeholder: '请输入企业名称（必填）'
    });

    this.setData({ formConfig: config });
  },

  /**
   * 表单数据变化处理
   */
  onFormChange(event) {
    const { formData, isValid, errors } = event.detail;
    
    console.log('表单数据变化:', formData);
    console.log('验证状态:', isValid);
    console.log('错误信息:', errors);

    this.setData({ currentFormData: formData });

    // 如果表单有效，可以进行搜索
    if (isValid && FormUtils.hasHighlight(formData)) {
      this.performSearch(formData);
    }
  },

  /**
   * 执行搜索
   */
  async performSearch(formData) {
    try {
      this.setData({ loading: true });
      
      // 格式化提交数据
      const submitData = FormUtils.formatForSubmit(formData);
      console.log('提交数据:', submitData);
      
      // 调用搜索API
      const results = await this.callSearchAPI(submitData);
      
      this.setData({ 
        searchResults: results,
        loading: false 
      });
      
      // 生成搜索摘要
      const summary = FormUtils.generateSummary(formData, this.data.formConfig);
      console.log('搜索摘要:', summary);
      
    } catch (error) {
      console.error('搜索失败:', error);
      this.setData({ loading: false });
      
      wx.showToast({
        title: '搜索失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 模拟搜索API调用
   */
  async callSearchAPI(data) {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 返回模拟数据
    return [
      { id: 1, name: '示例企业1', status: '在营' },
      { id: 2, name: '示例企业2', status: '在营' },
      { id: 3, name: '示例企业3', status: '在营' }
    ];
  },

  /**
   * 清空表单
   */
  onClearForm() {
    this.clearForm();
    this.setData({ 
      searchResults: [],
      currentFormData: {}
    });
    
    wx.showToast({
      title: '已清空搜索条件',
      icon: 'success'
    });
  },

  /**
   * 示例回填数据
   */
  onFillExample() {
    const exampleData = {
      ent_name: '示例科技有限公司',
      ent_status: ['RUN'],
      technology_types: ['HN', 'MST'],
      register_capital: [{
        start: '100',
        end: '1000',
        special: true
      }]
    };
    
    this.fillForm(exampleData);
    
    wx.showToast({
      title: '已回填示例数据',
      icon: 'success'
    });
  },

  /**
   * 验证表单
   */
  onValidateForm() {
    const validation = FormUtils.validateForm(this, '#configurableForm');
    
    if (validation) {
      if (validation.isValid) {
        wx.showToast({
          title: '表单验证通过',
          icon: 'success'
        });
      } else {
        const errorMessages = validation.errors.map(error => error.message).join('\n');
        wx.showModal({
          title: '表单验证失败',
          content: errorMessages,
          showCancel: false
        });
      }
    }
  },

  /**
   * 导出搜索结果
   */
  onExportResults() {
    if (this.data.searchResults.length === 0) {
      wx.showToast({
        title: '暂无搜索结果',
        icon: 'none'
      });
      return;
    }

    // 这里可以实现导出逻辑
    console.log('导出数据:', this.data.searchResults);
    
    wx.showToast({
      title: '导出功能开发中',
      icon: 'none'
    });
  },

  /**
   * 动态添加字段
   */
  onAddCustomField() {
    let config = [...this.data.formConfig];
    
    // 添加新字段
    config = FormUtils.addField(config, 'enterprise', {
      type: 'input',
      label: '法定代表人',
      key: 'legal_person',
      placeholder: '请输入法定代表人姓名',
      maxLength: 20
    });
    
    this.setData({ formConfig: config });
    
    wx.showToast({
      title: '已添加法定代表人字段',
      icon: 'success'
    });
  },

  /**
   * 动态移除字段
   */
  onRemoveField() {
    let config = [...this.data.formConfig];
    
    // 移除字段
    config = FormUtils.removeField(config, 'legal_person');
    
    this.setData({ formConfig: config });
    
    wx.showToast({
      title: '已移除自定义字段',
      icon: 'success'
    });
  },

  /**
   * VIP弹窗处理
   */
  onVipShow() {
    console.log('需要显示VIP弹窗');
    // 这里可以处理VIP相关逻辑
  }
});

/**
 * WXML使用示例
 */
const WXMLExample = `
<!-- 在页面wxml中使用 -->
<view class="page-container">
  <!-- 配置化表单 -->
  <configurable-form 
    id="configurableForm"
    config="{{formConfig}}"
    wrapHeight="calc(100vh - 200rpx)"
    componentType="hunt"
    bind:change="onFormChange"
    bind:vip="onVipShow">
  </configurable-form>
  
  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button bind:tap="onClearForm">清空条件</button>
    <button bind:tap="onFillExample">示例回填</button>
    <button bind:tap="onValidateForm">验证表单</button>
    <button bind:tap="onExportResults">导出结果</button>
    <button bind:tap="onAddCustomField">添加字段</button>
    <button bind:tap="onRemoveField">移除字段</button>
  </view>
  
  <!-- 搜索结果 -->
  <view wx:if="{{searchResults.length > 0}}" class="search-results">
    <view class="result-header">搜索结果 ({{searchResults.length}})</view>
    <view wx:for="{{searchResults}}" wx:key="id" class="result-item">
      <text>{{item.name}} - {{item.status}}</text>
    </view>
  </view>
  
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading">
    <text>搜索中...</text>
  </view>
</view>
`;

/**
 * JSON配置示例
 */
const JSONExample = `
{
  "usingComponents": {
    "configurable-form": "/components/ConfigurableForm/index"
  }
}
`;

module.exports = {
  WXMLExample,
  JSONExample
};
