<view class="form-input-container" catchtap="preventBubble">
  <view class="input-header">
    <view class="input-label">{{field.label}}</view>
    <view wx:if="{{field.required}}" class="required-mark">*</view>
    <view wx:if="{{field.vip}}" class="vip-badge">VIP</view>
  </view>
  
  <view class="input-wrapper">
    <input class="input-field"
           placeholder="{{field.placeholder}}"
           placeholder-class="input-placeholder"
           value="{{value}}"
           maxlength="{{field.maxLength || 50}}"
           disabled="{{disabled}}"
           bindfocus="onFocus"
           bindblur="onBlur"
           bindinput="onInput" />
    
    <!-- 清空按钮 -->
    <view wx:if="{{value && !disabled}}" 
          class="clear-btn" 
          catchtap="clearInput">
      <image src="/image/icon-close.png" mode="aspectFit"></image>
    </view>
  </view>

  <!-- 搜索建议列表 -->
  <view wx:if="{{focus && showSearchList && searchList.length > 0}}" 
        class="search-suggestions"
        catchtouchmove="preventBubble">
    <scroll-view class="suggestions-list" scroll-y scroll-with-animation>
      <view wx:for="{{searchList}}" 
            wx:key="index" 
            wx:for-item="item"
            class="suggestion-item"
            hover-class="suggestion-hover"
            catchtap="onSearchItemClick"
            data-item="{{item}}">
        <view class="suggestion-text">
          <block wx:for="{{item.ent_name}}" wx:key="index">
            <text class="suggestion-highlight {{item === value ? 'highlight' : ''}}">{{item}}</text>
          </block>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{focus && showSearchList && searchList.length === 0}}" 
        class="search-loading">
    <view class="loading-spinner"></view>
    <view class="loading-text">搜索中...</view>
  </view>
</view>
