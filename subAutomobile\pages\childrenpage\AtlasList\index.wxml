<import src="/template/null/null.wxml" />
<view class="pages">
  <view class="page_top">
    <!-- 返回路由 -->
    <view class="biaoqianf">
      <view class="biaoqian">
        <block wx:for="{{routeList}}" wx:key="index">
          <!--  bindtap="backRoute" -->
          <view
            class="{{index == routeList.length - 1 &&'one'}}"
            data-item="{{item}}"
            data-index="{{index}}"
            >{{item.name}}</view
          >
          <view class="two" wx:if="{{index != routeList.length - 1 }}"></view>
        </block>
      </view>
    </view>
    <view class="twonav">
      <view class="company_num"
        >共有 <text class="color_num">{{company_num}}</text> 家企业</view
      >
      <view class="head_left">
        <view
          class="left_box {{item.show || item.isVal?'active':''}}"
          bindtap="headTap"
          wx:for="{{popSort}}"
          wx:key="index"
          data-item="{{index}}"
        >
          <text class="down_txt" data-item="{{index}}">{{item.name}}</text>
          <view class="iconas flex_all_center " wx:if="{{levelShow}}">
            <image
              src="{{icon || 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/menu-up.png' }}"
            ></image>
          </view>
          <!-- 关闭下拉---还有两种情况 是否 -->
          <image
            wx:if="{{!item.show && !item.isVal}}"
            class="down_img"
            src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/menu-down.png"
            data-item="{{index}}"
          ></image>
          <image
            wx:if="{{!item.show && item.isVal}}"
            class="down_img"
            src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/menu-down-a.png"
            data-item="{{index}}"
          ></image>
          <image
            wx:if="{{item.show}}"
            class="down_img"
            src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/menu-up.png"
            data-item="{{index}}"
          ></image>
        </view>
      </view>
    </view>
    <!-- 写死的样式 -->
    <view class="threenav">
      <block wx:if="{{!tagArr.length}}">
        <view style="border: none;"></view>
      </block>
      <block wx:if="{{tagArr.length}}">
        <block wx:for="{{tagArr}}" wx:key="{{index}}">
          <view>{{item}}</view>
        </block>
      </block>
      <view class="end" bindtap="backRoute" data-ditu="{{true}}">地图展示</view>
    </view>
  </view>
  <!-- 排序下拉 -->
  <HalfScreenPop
    showCloseBtn="{{false}}"
    disableAnimation="{{true}}"
    startDistance="178rpx"
    showFooter="{{false}}"
    visible="{{popSort[1].show}}"
    bindclose="closeSort1"
    zIndex="{{100}}"
  >
    <view slot="customContent" class="sort-list">
      <scroll-view scroll-y="{{true}}" style="padding: 0 24rpx 0 32rpx;">
        <view
          wx:for="{{sortList}}"
          wx:key="index"
          class="sort_list {{ item.show?'active':'' }}"
          bindtap="chooseSort"
          data-item="{{index}}"
        >
          <text data-item="{{index}}">{{item.name}}</text>
          <image
            data-item="{{index}}"
            wx:if="{{item.show}}"
            src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/pickon.png"
          ></image>
        </view>
      </scroll-view>
    </view>
  </HalfScreenPop>
  <!-- 筛选下拉 -->
  <HalfScreenPop
    showCloseBtn="{{false}}"
    disableAnimation="{{true}}"
    startDistance="178rpx"
    showFooter="{{false}}"
    visible="{{popSort[0].show}}"
    bindclose="closeSort0"
    zIndex="{{100}}"
  >
    <view slot="customContent" class="sort-list">
      <view class="sort-list-box" style="height: {{computedHeight}}px;">
        <!-- 筛选组件 -->
        <Hunt
          wrapHeight="{{computedHeight-86}}px"
          bindsubmit="getParams"
          id="hunt"
          componentType="huntCopy"
        />
        <view class="footer" style="height: 85px;">
          <text class="reset" bindtap="resetCondition">重置</text>
          <text bindtap="search">确定</text>
        </view>
      </view>
    </view>
  </HalfScreenPop>
  <!-- 企业列表  -->
  <view>
    <block wx:if="{{requestData.length>0}}">
      <view class="card-box">
        <Card
          souceList="{{requestData}}"
          bindcardFun="onCard"
          bindloadMore="loadMore"
          bindrefresh="handleRefresher"
        ></Card>
      </view>
    </block>
    <block wx:if="{{requestData.length <= 0 }}">
      <view class="queshen" style="height: 100vh;">
        <template is="null" />
      </view>
    </block>
  </view>

  <!-- 联系方式弹窗 -->
  <dialog
    visible="{{showContact}}"
    title="联系方式"
    isShowConfirm="{{false}}"
    cancelBtnText="关闭"
    bindclose="onCloseContact"
  >
    <view class="dialog-con">
      <view class="contact_box" wx:for="{{contactList}}" wx:key="index">
        <view class="contact_left" bindtap="makeCall" data-item="{{item}}">
          <image
            src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>"
          ></image>
          <text class="contact_number">{{item.contact_data}}</text>
        </view>
        <view class="contact_right">{{item.sources[0]}}</view>
      </view>
    </view>
  </dialog>

  <!-- 地址弹窗 -->
  <dialog
    visible="{{showAddress}}"
    title="地址"
    isShowConfirm="{{false}}"
    cancelBtnText="关闭"
    bindclose="onCloseAddress"
  >
    <view class="dialog-con">
      <map
        id="map"
        longitude="{{location.lon}}"
        latitude="{{location.lat}}"
        markers="{{markers}}"
        scale="15"
        style="width: 100%; height: 500rpx;"
      >
      </map>
      <view style="margin: 20rpx 0;">{{locationTxt}}</view>
    </view>
  </dialog>
</view>
