/**
 * 配置化表单组件
 * 通过配置数组驱动表单渲染，类似PC表单封装
 */

const FormRenderer = require('./core/FormRenderer.js');
const FormValidator = require('./core/FormValidator.js');
const FormDataProcessor = require('./core/FormDataProcessor.js');
const { FORM_CONFIG, DEFAULT_PARAMS } = require('../hunt/common/config.js');

Component({
  properties: {
    // 表单配置数组
    config: {
      type: Array,
      value: FORM_CONFIG
    },
    // 容器高度
    wrapHeight: {
      type: String,
      value: 'calc(100vh - 200rpx)'
    },
    // 是否为页面模式
    isPage: {
      type: Boolean,
      value: false
    },
    // 组件类型（hunt/huntCopy）
    componentType: {
      type: String,
      value: 'hunt'
    }
  },

  data: {
    // 表单数据
    formData: {},
    // 渲染列表
    renderList: [],
    // 左侧导航列表
    leftNavList: [],
    // 当前激活的分组
    activeGroup: '',
    // 弹窗状态
    popupStates: {
      regionPop: false,
      eleseicPop: false,
      enttypePop: false,
      districtPop: false,
      chainCodePop: false,
      datePop: false
    },
    // 输入框状态
    inputStates: {
      focus: false,
      searchList: []
    },
    // VIP状态
    vipVisible: false
  },

  lifetimes: {
    attached() {
      this.initializeForm();
    }
  },

  methods: {
    /**
     * 初始化表单
     */
    initializeForm() {
      const config = this.filterConfigByComponent();
      const renderList = FormRenderer.generateRenderList(config);
      const leftNavList = FormRenderer.generateLeftNavList(config);
      
      this.setData({
        formData: { ...DEFAULT_PARAMS },
        renderList,
        leftNavList,
        activeGroup: config[0]?.groupKey || ''
      });
    },

    /**
     * 根据组件类型过滤配置
     */
    filterConfigByComponent() {
      const { componentType } = this.data;
      let config = this.data.config;

      // huntCopy组件排除产业链相关字段
      if (componentType === 'huntCopy') {
        config = config.map(group => ({
          ...group,
          fields: group.fields.filter(field => field.key !== 'chain_codes')
        })).filter(group => group.fields.length > 0);
      }

      return config;
    },

    /**
     * 处理字段值变化
     */
    onFieldChange(event) {
      const { field, value } = event.detail;
      const newFormData = FormDataProcessor.updateFieldValue(
        this.data.formData,
        field,
        value
      );

      this.setData({ formData: newFormData });
      this.emitFormChange();
    },

    /**
     * 处理标签选择
     */
    onTagSelect(event) {
      const { field, option, isMultiple } = event.detail;
      const newFormData = FormDataProcessor.handleTagSelect(
        this.data.formData,
        field,
        option,
        isMultiple
      );

      this.setData({ formData: newFormData });
      this.emitFormChange();
    },

    /**
     * 处理弹窗提交
     */
    onPopupSubmit(event) {
      const { field, data } = event.detail;
      const newFormData = FormDataProcessor.handlePopupData(
        this.data.formData,
        field,
        data
      );

      this.setData({ formData: newFormData });
      this.emitFormChange();
    },

    /**
     * 清空表单
     */
    clearForm() {
      this.setData({
        formData: { ...DEFAULT_PARAMS }
      });
      this.emitFormChange();
    },

    /**
     * 设置表单数据（回填）
     */
    setFormData(data) {
      const processedData = FormDataProcessor.processBackfillData(data);
      this.setData({
        formData: { ...this.data.formData, ...processedData }
      });
    },

    /**
     * 验证表单
     */
    validateForm() {
      return FormValidator.validate(this.data.formData, this.data.config);
    },

    /**
     * 获取表单数据
     */
    getFormData() {
      return FormDataProcessor.getSubmitData(this.data.formData);
    },

    /**
     * 发送表单变化事件
     */
    emitFormChange() {
      const formData = this.getFormData();
      const validation = this.validateForm();
      
      this.triggerEvent('change', {
        formData,
        isValid: validation.isValid,
        errors: validation.errors
      });
    },

    /**
     * 左侧导航点击
     */
    onLeftNavClick(event) {
      const { groupKey } = event.currentTarget.dataset;
      this.setData({ activeGroup: groupKey });
    },

    /**
     * 处理VIP权限检查
     */
    async checkVipPermission(field) {
      if (!field.vip) return true;
      
      // 这里可以调用权限检查逻辑
      // 暂时返回true，实际项目中需要实现具体逻辑
      return true;
    }
  }
});
