/**
 * 配置化表单组件 - 合并优化版本
 * 通过配置数组驱动表单渲染，整合所有必要功能到单一文件
 */

const {clone} = require('../../utils/util.js');
const {hasPrivile} = require('../../utils/route.js');
const {debounce} = require('../../utils/formate.js');

// ==================== 配置数据 ====================
const DEFAULT_PARAMS = {
  ent_name: '',
  regionData: [],
  eleseic_data: [],
  technology_types: [],
  listed_status: [],
  super_dimension_patent_category: [],
  expand_status: [],
  ent_scale: [],
  benefit_assess: [],
  register_time: [],
  register_capital: [],
  ent_status: [],
  shit_type: [],
  enttype_data: [],
  all_cert_data: [],
  contact_style: [],
  tel_data: [],
  email_data: [],
  super_dimension_social_num: [],
  super_dimension_biding: [],
  super_dimension_job_info: [],
  tax_credit: [],
  financing_info: [],
  super_dimension_trademark: [],
  super_dimension_patent: [],
  super_dimension_android_app: [],
  super_dimension_ios_app: [],
  super_dimension_mini_app: [],
  super_dimension_wx_extension: [],
  super_dimension_weibo_extension: [],
  untrustworthy_info_data: [],
  judgment_doc_data: [],
  adminstrative_penalties_data: [],
  chattel_mortage_data: [],
  abnormal_operation_data: [],
  software_copyright_data: [],
  work_copyright_data: [],
  super_dimension_website: [],
  super_dimension_icp: [],
  chain_codes_data: [],
  leading_ent: []
};

// PC表单风格的配置数组
const FORM_CONFIG = [
  {
    groupTitle: '基础筛选',
    groupKey: 'basic',
    fields: [
      {
        type: 'input',
        label: '企业名称',
        key: 'ent_name',
        placeholder: '请输入企业名称',
        maxLength: 50,
        special: 'input'
      },
      {
        type: 'pop',
        label: '所在地区',
        key: 'areas',
        dataKey: 'regionData',
        popType: 'region',
        special: 'pop'
      },
      {
        type: 'pop',
        label: '所属行业',
        key: 'trade_types',
        dataKey: 'eleseic_data',
        popType: 'industry',
        special: 'pop'
      }
    ]
  },
  {
    groupTitle: '产业优选',
    groupKey: 'industry',
    fields: [
      {
        type: 'radio',
        label: '龙头企业',
        key: 'leading_ent',
        vip: true,
        options: [
          {value: 'true', label: '是龙头企业'},
          {value: 'false', label: '非龙头企业'}
        ]
      },
      {
        type: 'multiSelect',
        label: '疑似扩张',
        key: 'expand_status',
        vip: true,
        options: [
          {value: 'A', label: '近期融资'},
          {value: 'B', label: '近期新增分支机构'},
          {value: 'C', label: '近期疑似扩张'}
        ]
      },
      {
        type: 'pop',
        label: '所属产业链',
        key: 'chain_codes',
        dataKey: 'chain_codes_data',
        popType: 'chain',
        vip: true,
        special: 'pop'
      },
      {
        type: 'select',
        label: '企业规模',
        key: 'ent_scale',
        icon: true,
        vip: true,
        options: [
          {value: '0$500', label: '微型'},
          {value: '500$1000', label: '小微型'},
          {value: '1000$2000', label: '中小型'}
        ]
      },
      {
        type: 'multiSelect',
        label: '科技型企业',
        key: 'technology_types',
        vip: true,
        isOpenIcon: true,
        isOpen: true,
        options: [
          {value: 'HN', label: '高新技术企业'},
          {value: 'MST', label: '科技型中小企业'},
          {value: 'PSN', label: '专精特新企业'}
        ]
      }
    ]
  },
  {
    groupTitle: '企业相关',
    groupKey: 'enterprise',
    fields: [
      {
        type: 'datePop',
        label: '注册时间',
        key: 'register_time',
        minLabel: '最低年限',
        maxLabel: '最高年限',
        unit: '年',
        genre: 'tpop',
        options: [
          {value: '0$1', label: '1年内'},
          {value: '1$2', label: '1-2年'}
        ]
      },
      {
        type: 'rangeInput',
        label: '注册资本',
        key: 'register_capital',
        minLabel: '最低资本',
        maxLabel: '最高资本',
        unit: '万元',
        genre: 'input',
        options: [
          {value: '0$100', label: '100万以下'},
          {value: '100$200', label: '100-200万'}
        ]
      },
      {
        type: 'multiSelect',
        label: '企业状态',
        key: 'ent_status',
        options: [
          {value: 'RUN', label: '在营'},
          {value: 'REVOKE', label: '吊销'},
          {value: 'LOGOUT', label: '注销'}
        ]
      },
      {
        type: 'pop',
        label: '企业许可',
        key: 'ent_cert',
        dataKey: 'all_cert_data',
        popType: 'cert',
        vip: true,
        special: 'pop'
      },
      {
        type: 'pop',
        label: '企业类型',
        key: 'ent_type',
        dataKey: 'enttype_data',
        popType: 'entType',
        special: 'pop'
      },
      {
        type: 'multiSelect',
        label: '实体类型',
        key: 'shit_type',
        options: [
          {value: 'ENTERPRISE', label: '企业(不包含个体户)'},
          {value: 'INDIVIDUAL', label: '个体户'}
        ]
      }
    ]
  },
  {
    groupTitle: '经营状态',
    groupKey: 'business',
    fields: [
      {
        type: 'radio',
        label: '联系方式',
        key: 'tel_data',
        vip: true,
        options: [
          {value: 'TEL', label: '有'},
          {value: 'NO_TEL', label: '无'}
        ]
      },
      {
        type: 'radio',
        label: '手机号码',
        key: 'contact_style',
        options: [
          {value: 'PHONE', label: '有'},
          {value: 'NO_PHONE', label: '无'}
        ]
      },
      {
        type: 'radio',
        label: '联系邮箱',
        key: 'email_data',
        options: [
          {value: 'EMAIL', label: '有'},
          {value: 'NO_EMAIL', label: '无'}
        ]
      },
      {
        type: 'rangeInput',
        label: '参保人数',
        key: 'super_dimension_social_num',
        minLabel: '最低人数',
        maxLabel: '最高人数',
        unit: '人',
        genre: 'input',
        options: [
          {value: '0$49', label: '0-49'},
          {value: '50$99', label: '50-99'}
        ]
      },
      {
        type: 'multiSelect',
        label: '融资信息',
        key: 'financing_info',
        vip: true,
        isOpenIcon: true,
        isOpen: true,
        options: [
          {value: '天使轮', label: '天使轮'},
          {value: 'A轮', label: 'A轮'},
          {value: 'B轮', label: 'B轮'}
        ]
      },
      {
        type: 'multiSelect',
        label: '上市状态',
        key: 'listed_status',
        vip: true,
        options: [
          {value: 'LISTED_A', label: 'A股'},
          {value: 'LISTED_B', label: 'B股'}
        ]
      },
      {
        type: 'radio',
        label: '招投标',
        key: 'super_dimension_biding',
        vip: true,
        options: [
          {value: 'true', label: '有'},
          {value: 'false', label: '无'}
        ]
      },
      {
        type: 'radio',
        label: '招聘',
        key: 'super_dimension_job_info',
        vip: true,
        options: [
          {value: 'true', label: '有'},
          {value: 'false', label: '无'}
        ]
      },
      {
        type: 'radio',
        label: '纳税信用',
        key: 'tax_credit',
        vip: true,
        options: [
          {value: 'ALEVEL', label: 'A级'},
          {value: 'NONALEVEL', label: '非A级'}
        ]
      }
    ]
  }
];

// ==================== 工具类 ====================
class FormUtils {
  // 深拷贝
  static deepClone(obj) {
    return JSON.parse(JSON.stringify(obj));
  }

  // 防抖
  static debounce(func, delay = 300) {
    let timeoutId;
    return function (...args) {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
  }

  // 检查值是否有效
  static isValidValue(value) {
    if (value === null || value === undefined || value === '') return false;
    if (Array.isArray(value)) return value.length > 0;
    return true;
  }

  // 获取高亮状态
  static getHighlightStatus(formData) {
    return Object.keys(formData).some(key => {
      const value = formData[key];
      if (typeof value === 'string') return value.trim().length > 0;
      if (Array.isArray(value)) {
        if (value.length === 0) return false;
        if (
          value[0] &&
          typeof value[0] === 'object' &&
          value[0].start !== undefined
        ) {
          return value[0].start || value[0].end;
        }
        return true;
      }
      return false;
    });
  }

  // 处理多选地区数据
  static handleMultipleRegionData(data = []) {
    if (!data.length) return [];
    const tempData = this.deepClone(data);
    const checkedData = tempData.filter(
      item => item.status === 'checked' && !item.ischildren
    );
    const filteredData = checkedData.filter(
      item => !checkedData.map(parent => parent.code).includes(item.parent)
    );
    if (!filteredData.length) return data;
    filteredData.forEach(parent => {
      const index = tempData.findIndex(child => parent.code === child.parent);
      if (index > -1) tempData.splice(index, 1);
    });
    return tempData;
  }

  // 获取弹窗显示文本
  static getPopDisplayText(data) {
    if (!Array.isArray(data) || data.length === 0) return '全部';
    if (data[0]?.MultipleCelectionSingleSelection) return data[0].name;
    const selectedItems = data.filter(item => item.status === 'checked');
    if (selectedItems.length === 0) return '全部';
    return selectedItems.map(item => item.name).join('、');
  }
}

Component({
  properties: {
    config: {type: Array, value: FORM_CONFIG},
    wrapHeight: {type: String, value: 'calc(100vh - 200rpx)'},
    isPage: {type: Boolean, value: false},
    componentType: {type: String, value: 'hunt'}
  },

  data: {
    isIphoneX: getApp().globalData.isIphoneX,
    login: getApp().globalData.login,
    params: FormUtils.deepClone(DEFAULT_PARAMS),
    itemList: [],
    leftList: [],
    activeGroup: '',

    // 输入框状态
    minCapital: '',
    maxCapital: '',
    dateType: '',
    dateActive: false,
    date: '',
    capitalActive: false,
    minDate: '',
    maxDate: '',
    socialActive: false,
    socialminPeson: '',
    socialmaxPeson: '',

    // 弹窗状态
    regionPop: false,
    datePop: false,
    eleseicPop: false,
    enttypePop: false,
    districtPop: false,
    chainCodePop: false,
    vipVisible: false,

    // 其他状态
    idName: '',
    focus: false,
    searchList: [],
    isHeight: false,
    paramsData: {}
  },

  lifetimes: {
    attached() {
      this.initializeForm();
    }
  },

  observers: {
    params: function (val) {
      this.result(val);
    }
  },

  methods: {
    /**
     * 初始化表单
     */
    initializeForm() {
      const config = this.filterConfigByComponent();
      const {renderList, leftNavList} = this.generateRenderData(config);

      this.setData({
        itemList: renderList,
        leftList: leftNavList,
        activeGroup: config[0]?.groupKey || ''
      });
    },

    /**
     * 根据组件类型过滤配置
     */
    filterConfigByComponent() {
      const {componentType} = this.data;
      let config = FormUtils.deepClone(this.data.config);

      if (componentType === 'huntCopy') {
        config = config
          .map(group => ({
            ...group,
            fields: group.fields.filter(field => field.key !== 'chain_codes')
          }))
          .filter(group => group.fields.length > 0);
      }

      return config;
    },

    /**
     * 生成渲染数据
     */
    generateRenderData(config) {
      const renderList = [];
      const leftNavList = [];

      config.forEach(group => {
        // 生成左侧导航
        leftNavList.push({
          title: group.groupTitle,
          onlyText: true,
          isOpen: true,
          isActive: false,
          map: group.fields.map(field => ({
            key: field.key,
            value: field.label,
            active: false
          }))
        });

        // 生成渲染项
        group.fields.forEach(field => {
          const renderItem = this.createRenderItem(field);
          renderList.push(renderItem);
        });
      });

      return {renderList, leftNavList};
    },

    /**
     * 创建渲染项
     */
    createRenderItem(field) {
      const baseItem = {
        type: field.key,
        title: field.label,
        key: field.key,
        fieldType: field.type,
        vip: field.vip || false,
        icon: field.icon || false,
        special: field.special || field.type,
        content: ''
      };

      switch (field.type) {
        case 'input':
          return {
            ...baseItem,
            placeholder: field.placeholder,
            maxLength: field.maxLength
          };
        case 'select':
        case 'multiSelect':
        case 'radio':
          return {
            ...baseItem,
            list: (field.options || []).map(opt => ({
              id: opt.value,
              name: opt.label,
              active: false
            })),
            isOpenIcon: field.isOpenIcon || false,
            isOpen: field.isOpen !== false
          };
        case 'rangeInput':
          return {
            ...baseItem,
            min: field.minLabel,
            max: field.maxLabel,
            unit: field.unit,
            genre: field.genre,
            list: (field.options || []).map(opt => ({
              id: opt.value,
              name: opt.label,
              active: false
            }))
          };
        case 'datePop':
          return {
            ...baseItem,
            min: field.minLabel,
            max: field.maxLabel,
            unit: field.unit,
            genre: field.genre,
            list: (field.options || []).map(opt => ({
              id: opt.value,
              name: opt.label,
              active: false
            }))
          };
        case 'pop':
          return {...baseItem, dataKey: field.dataKey, popType: field.popType};
        default:
          return baseItem;
      }
    },

    /**
     * 清空搜索条件
     */
    clearSear() {
      const resetParams = FormUtils.deepClone(DEFAULT_PARAMS);
      const list = this.data.itemList.map(item => {
        if (item.list) {
          item.list = item.list.map(tag => ({...tag, active: false}));
        }
        if (item.isOpenIcon) {
          const currentItem = this.data.itemList.find(
            i => item.type === i.type
          );
          item.isOpen = currentItem?.isOpen || false;
        }
        item.content = '';
        return item;
      });

      this.setData({
        itemList: list,
        params: resetParams,
        minCapital: '',
        maxCapital: '',
        capitalActive: false,
        minDate: '',
        maxDate: '',
        dateActive: false,
        socialminPeson: '',
        socialmaxPeson: '',
        socialActive: false
      });
    },

    /**
     * 设置回填数据
     */
    setBackfillData(tempObj = {}) {
      this.clearSear();
      let {itemList, params} = this.data;
      let resetStates = {
        minCapital: '',
        maxCapital: '',
        capitalActive: false,
        minDate: '',
        maxDate: '',
        dateActive: false,
        socialminPeson: '',
        socialmaxPeson: '',
        socialActive: false
      };

      params = Object.assign(params, tempObj);

      // 处理回填逻辑
      itemList = itemList.map(item => {
        for (let key in params) {
          if (item.type === key) {
            this.handleBackfillItem(item, key, params[key], resetStates);
          }
        }
        return item;
      });

      this.setData({params, itemList, ...resetStates});
    },

    /**
     * 处理回填项目
     */
    handleBackfillItem(item, key, value, resetStates) {
      // 处理弹窗类型
      const popFieldsMap = {
        areas: 'regionData',
        trade_types: 'eleseic_data',
        ent_type: 'enttype_data',
        ent_cert: 'all_cert_data',
        chain_codes: 'chain_codes_data'
      };

      if (popFieldsMap[key]) {
        const dataKey = popFieldsMap[key];
        const obj = this.data.params[dataKey];
        if (obj?.length > 0) {
          item.content = FormUtils.getPopDisplayText(obj);
        }
        return;
      }

      // 处理范围输入类型
      if (
        key === 'register_capital' &&
        Array.isArray(value) &&
        value[0]?.special
      ) {
        resetStates.minCapital = value[0].start || '';
        resetStates.maxCapital = value[0].end || '';
        resetStates.capitalActive = !!(value[0].start || value[0].end);
        return;
      }

      if (
        key === 'super_dimension_social_num' &&
        Array.isArray(value) &&
        value[0]?.special
      ) {
        resetStates.socialminPeson = value[0].start || '';
        resetStates.socialmaxPeson = value[0].end || '';
        resetStates.socialActive = !!(value[0].start || value[0].end);
        return;
      }

      if (
        key === 'register_time' &&
        Array.isArray(value) &&
        value[0]?.special
      ) {
        resetStates.minDate = value[0].start || '';
        resetStates.maxDate = value[0].end || '';
        resetStates.dateActive = !!(value[0].start || value[0].end);
        return;
      }

      // 处理标签类型
      if (item.list && Array.isArray(value)) {
        item.list = item.list.map(tag => ({
          ...tag,
          active: value.includes(tag.id)
        }));
      }
    },

    /**
     * 选择标签
     */
    async selectTag(event) {
      const {id, type, name, item} = event.currentTarget.dataset;

      // VIP权限检查
      if (item?.vip && !(await this.checkVipPermission())) {
        return;
      }

      let {itemList, params} = this.data;
      const currentItem = itemList.find(i => i.type === type);

      if (!currentItem || !currentItem.list) return;

      // 处理标签选择逻辑
      const isMultiSelect = ['multiSelect', 'select'].includes(
        currentItem.fieldType
      );
      const currentValue = params[type] || [];

      if (isMultiSelect) {
        // 多选逻辑
        const index = currentValue.indexOf(id);
        if (index > -1) {
          currentValue.splice(index, 1);
        } else {
          currentValue.push(id);
        }
      } else {
        // 单选逻辑
        params[type] = currentValue.includes(id) ? [] : [id];
      }

      // 更新标签状态
      currentItem.list = currentItem.list.map(tag => ({
        ...tag,
        active: params[type].includes(tag.id)
      }));

      // 清除对应的输入框
      this.clearInputsByType(type);

      this.setData({itemList, params});
    },

    /**
     * 检查VIP权限
     */
    async checkVipPermission() {
      try {
        const str = await hasPrivile({packageType: true});
        if (str === '游客') {
          getApp().route(this, '/pages/login/login');
          return false;
        } else if (str === '普通VIP') {
          this.setData({vipVisible: true});
          return false;
        }
        return true;
      } catch (error) {
        console.error('权限检查失败:', error);
        return false;
      }
    },

    /**
     * 根据类型清除输入框
     */
    clearInputsByType(type) {
      const clearMap = {
        register_capital: {
          minCapital: '',
          maxCapital: '',
          capitalActive: false
        },
        super_dimension_social_num: {
          socialminPeson: '',
          socialmaxPeson: '',
          socialActive: false
        },
        register_time: {minDate: '', maxDate: '', dateActive: false}
      };

      const clearStates = clearMap[type];
      if (clearStates) {
        this.setData(clearStates);
      }
    },

    /**
     * 输出搜索结果
     */
    result(val) {
      const paramsData = val || this.data.params;
      const isHeight = FormUtils.getHighlightStatus(paramsData);
      const processedData = this.handleData(paramsData);
      const finalData = JSON.parse(processedData);

      this.setData({paramsData: finalData, isHeight});

      this.triggerEvent('submit', {
        isHeight,
        paramsData: clone(finalData)
      });
    },

    /**
     * 处理数据
     */
    handleData(params) {
      let obj = clone(params);

      // 处理弹窗相关数据
      obj['areas'] =
        params.regionData?.length > 0
          ? FormUtils.handleMultipleRegionData(params.regionData)
              .filter(i => i.status === 'checked')
              .map(i => i.code)
          : [];
      obj['trade_types'] =
        params.eleseic_data?.length > 0
          ? FormUtils.handleMultipleRegionData(params.eleseic_data)
              .filter(i => i.status === 'checked')
              .map(i => i.code)
          : [];
      obj['ent_type'] =
        params.enttype_data?.length > 0
          ? FormUtils.handleMultipleRegionData(params.enttype_data)
              .filter(i => i.status === 'checked')
              .map(i => i.code)
          : [];
      obj['ent_cert'] =
        params.all_cert_data?.length > 0
          ? FormUtils.handleMultipleRegionData(params.all_cert_data)
              .filter(i => i.status === 'checked')
              .map(i => i.code)
          : [];
      obj['chain_codes'] =
        params.chain_codes_data?.length > 0
          ? params.chain_codes_data
              .filter(i => i.active === true)
              .map(i => i.code)
          : [];

      // 处理布尔值
      const boolFields = [
        'super_dimension_biding',
        'super_dimension_job_info',
        'super_dimension_trademark',
        'super_dimension_patent',
        'super_dimension_android_app',
        'super_dimension_ios_app',
        'super_dimension_mini_app',
        'super_dimension_wx_extension',
        'super_dimension_weibo_extension',
        'super_dimension_website',
        'super_dimension_icp',
        'leading_ent'
      ];

      boolFields.forEach(field => {
        if (obj[field]?.length) {
          obj[field] = JSON.parse(obj[field][0]);
        } else {
          delete obj[field];
        }
      });

      // 处理父级对象
      const parentPrefixes = ['super_dimension_'];
      parentPrefixes.forEach(prefix => {
        Object.keys(obj).forEach(key => {
          if (key.startsWith(prefix)) {
            const suffix = key.slice(prefix.length);
            const parentKey = prefix.slice(0, -1);
            const fieldObj = {};
            fieldObj[suffix] = obj[key];
            obj[parentKey] = {...obj[parentKey], ...fieldObj};
            delete obj[key];
          }
        });
      });

      return JSON.stringify(obj);
    },

    // ==================== 事件处理方法 ====================

    /**
     * 左侧导航点击
     */
    closeleft(event) {
      const {item} = event.currentTarget.dataset;
      let {leftList} = this.data;

      leftList = leftList.map(left => {
        if (left.title === item.title) {
          left.isOpen = !left.isOpen;
        }
        return left;
      });

      this.setData({leftList});
    },

    /**
     * 左侧子项点击
     */
    leftactvie(event) {
      const {item, itm} = event.currentTarget.dataset;
      this.setData({idName: itm});
    },

    /**
     * 展开/收起右侧项目
     */
    openright(event) {
      const {item} = event.currentTarget.dataset;
      let {itemList} = this.data;

      itemList = itemList.map(i => {
        if (i.type === item.type) {
          i.isOpen = !i.isOpen;
        }
        return i;
      });

      this.setData({itemList});
    },

    /**
     * 输入框相关事件
     */
    onFocus() {
      this.setData({focus: true});
    },

    onBlur() {
      setTimeout(() => {
        this.setData({focus: false});
      }, 200);
    },

    onInput: FormUtils.debounce(function (event) {
      const value = event.detail.value;
      let {params} = this.data;
      params.ent_name = value;
      this.setData({params});

      if (value.trim()) {
        this.searchCompanies(value);
      } else {
        this.setData({searchList: []});
      }
    }, 300),

    /**
     * 搜索企业
     */
    async searchCompanies(keyword) {
      try {
        // 这里应该调用实际的搜索API
        const mockResults = [
          {ent_name: [`${keyword}科技有限公司`]},
          {ent_name: [`${keyword}实业有限公司`]},
          {ent_name: [`${keyword}贸易有限公司`]}
        ];
        this.setData({searchList: mockResults});
      } catch (error) {
        console.error('搜索企业失败:', error);
        this.setData({searchList: []});
      }
    },

    /**
     * 点击搜索项
     */
    clickItem(event) {
      const {item} = event.currentTarget.dataset;
      let {params} = this.data;
      params.ent_name = item.ent_name[0];
      this.setData({params, focus: false, searchList: []});
    },

    /**
     * 范围输入处理
     */
    capitalInput(event) {
      const {type} = event.currentTarget.dataset;
      const value = event.detail.value;
      const updateData = {};

      if (type === 'min') {
        updateData.minCapital = value;
      } else {
        updateData.maxCapital = value;
      }

      this.setData(updateData);
      this.updateRangeParams(
        'register_capital',
        'minCapital',
        'maxCapital',
        'capitalActive'
      );
    },

    socialInput(event) {
      const {type} = event.currentTarget.dataset;
      const value = event.detail.value;
      const updateData = {};

      if (type === 'min') {
        updateData.socialminPeson = value;
      } else {
        updateData.socialmaxPeson = value;
      }

      this.setData(updateData);
      this.updateRangeParams(
        'super_dimension_social_num',
        'socialminPeson',
        'socialmaxPeson',
        'socialActive'
      );
    },

    dateInput(event) {
      const {type} = event.currentTarget.dataset;
      const value = event.detail.value;
      const updateData = {};

      if (type === 'min') {
        updateData.minDate = value;
      } else {
        updateData.maxDate = value;
      }

      this.setData(updateData);
      this.updateRangeParams(
        'register_time',
        'minDate',
        'maxDate',
        'dateActive'
      );
    },

    /**
     * 更新范围参数
     */
    updateRangeParams(paramKey, minKey, maxKey, activeKey) {
      const {[minKey]: minVal, [maxKey]: maxVal} = this.data;
      let {params} = this.data;

      if (minVal || maxVal) {
        params[paramKey] = [
          {
            start: minVal,
            end: maxVal,
            special: true
          }
        ];
        this.setData({[activeKey]: true});
      } else {
        params[paramKey] = [];
        this.setData({[activeKey]: false});
      }

      this.setData({params});
      this.clearTagsByType(paramKey);
    },

    /**
     * 清除标签
     */
    clearTagsByType(type) {
      let {itemList} = this.data;
      const item = itemList.find(i => i.type === type);

      if (item && item.list) {
        item.list = item.list.map(tag => ({...tag, active: false}));
        this.setData({itemList});
      }
    },

    /**
     * 弹窗相关事件
     */
    openRegionPop() {
      this.setData({regionPop: true});
    },

    openEleseicPop() {
      this.setData({eleseicPop: true});
    },

    openEnttypePop() {
      this.setData({enttypePop: true});
    },

    openDistrictPop() {
      this.setData({districtPop: true});
    },

    openChainCodePop() {
      this.setData({chainCodePop: true});
    },

    openDatePop(event) {
      const {type} = event.currentTarget.dataset;
      this.setData({
        datePop: true,
        dateType: type
      });
    },

    /**
     * 弹窗提交处理
     */
    submits(event) {
      const {mark, data} = event.detail;
      let {params} = this.data;

      // 关闭弹窗
      const popupMap = {
        areas: 'regionPop',
        trade_types: 'eleseicPop',
        ent_type: 'enttypePop',
        ent_cert: 'districtPop',
        chain_codes: 'chainCodePop'
      };

      const popupKey = popupMap[mark];
      if (popupKey) {
        this.setData({[popupKey]: false});
      }

      // 更新数据
      const dataKeyMap = {
        areas: 'regionData',
        trade_types: 'eleseic_data',
        ent_type: 'enttype_data',
        ent_cert: 'all_cert_data',
        chain_codes: 'chain_codes_data'
      };

      const dataKey = dataKeyMap[mark];
      if (dataKey) {
        params[dataKey] = data;
        this.setData({params});
        this.updatePopContent(mark, data);
      }
    },

    /**
     * 更新弹窗内容显示
     */
    updatePopContent(mark, data) {
      let {itemList} = this.data;
      const item = itemList.find(i => i.type === mark);

      if (item) {
        item.content = FormUtils.getPopDisplayText(data);
        this.setData({itemList});
      }
    },

    /**
     * 日期设置
     */
    setDate(event) {
      const {date} = event.detail;
      const {dateType} = this.data;

      if (dateType === 'min') {
        this.setData({minDate: date});
      } else {
        this.setData({maxDate: date});
      }

      this.setData({datePop: false});
      this.updateRangeParams(
        'register_time',
        'minDate',
        'maxDate',
        'dateActive'
      );
    },

    /**
     * VIP弹窗关闭
     */
    vipClose() {
      this.setData({vipVisible: false});
    },

    /**
     * 阻止事件冒泡
     */
    _event() {
      // 阻止事件冒泡
    },

    closeInptPop() {
      this.setData({focus: false});
    },

    return() {
      // 阻止滚动穿透
    },

    // ==================== 公共API方法 ====================

    /**
     * 清空表单 - 公共API
     */
    clearForm() {
      this.clearSear();
    },

    /**
     * 设置表单数据 - 公共API
     */
    setFormData(data) {
      this.setBackfillData(data);
    },

    /**
     * 获取表单数据 - 公共API
     */
    getFormData() {
      return this.data.paramsData;
    },

    /**
     * 验证表单 - 公共API
     */
    validateForm() {
      const isValid = FormUtils.getHighlightStatus(this.data.params);
      return {
        isValid,
        errors: isValid ? [] : ['请至少选择一个搜索条件']
      };
    }
  },

  // ==================== 页面生命周期 ====================

  pageLifetimes: {
    show() {
      const {wrapHeight, isIphoneX} = this.data;

      if (!wrapHeight) {
        this.setData({
          wrapHeight: `calc(100vh - ${isIphoneX ? '168rpx' : '110rpx'})`
        });
      }

      this.setBackfillData(this.data.paramsData);
    }
  }
});
