/**
 * 表单输入框组件
 * 可复用的输入框字段组件
 */

Component({
  properties: {
    // 字段配置
    field: {
      type: Object,
      value: {}
    },
    // 字段值
    value: {
      type: String,
      value: ''
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    }
  },

  data: {
    focus: false,
    searchList: [],
    showSearchList: false
  },

  methods: {
    /**
     * 输入框获取焦点
     */
    onFocus(event) {
      this.setData({ focus: true });
      
      // 如果是企业名称输入框，显示搜索建议
      if (this.data.field.key === 'ent_name' && this.data.value) {
        this.showSearchSuggestions();
      }
    },

    /**
     * 输入框失去焦点
     */
    onBlur(event) {
      // 延迟隐藏搜索列表，以便点击搜索项
      setTimeout(() => {
        this.setData({ 
          focus: false,
          showSearchList: false
        });
      }, 200);
    },

    /**
     * 输入内容变化
     */
    onInput(event) {
      const value = event.detail.value;
      
      this.triggerEvent('change', {
        field: this.data.field,
        value: value
      });

      // 如果是企业名称，显示搜索建议
      if (this.data.field.key === 'ent_name' && value.trim()) {
        this.searchCompanies(value);
      } else {
        this.setData({ showSearchList: false });
      }
    },

    /**
     * 搜索企业建议
     */
    async searchCompanies(keyword) {
      try {
        // 这里应该调用实际的搜索API
        // 暂时使用模拟数据
        const mockResults = [
          { ent_name: [`${keyword}科技有限公司`] },
          { ent_name: [`${keyword}实业有限公司`] },
          { ent_name: [`${keyword}贸易有限公司`] }
        ];

        this.setData({
          searchList: mockResults,
          showSearchList: true
        });
      } catch (error) {
        console.error('搜索企业失败:', error);
        this.setData({ showSearchList: false });
      }
    },

    /**
     * 点击搜索建议项
     */
    onSearchItemClick(event) {
      const item = event.currentTarget.dataset.item;
      const companyName = item.ent_name[0];
      
      this.triggerEvent('change', {
        field: this.data.field,
        value: companyName
      });

      this.setData({
        showSearchList: false,
        focus: false
      });
    },

    /**
     * 显示搜索建议
     */
    showSearchSuggestions() {
      if (this.data.value.trim()) {
        this.searchCompanies(this.data.value);
      }
    },

    /**
     * 清空输入框
     */
    clearInput() {
      this.triggerEvent('change', {
        field: this.data.field,
        value: ''
      });
      
      this.setData({ showSearchList: false });
    },

    /**
     * 阻止事件冒泡
     */
    preventBubble() {
      // 阻止事件冒泡
    }
  }
});
