/**
 * 配置化表单组件配置文件
 * 通过配置数组驱动表单渲染，类似PC表单封装
 */

// 默认搜索参数
const DEFAULT_PARAMS = {
  ent_name: '',
  regionData: [],
  eleseic_data: [],
  technology_types: [],
  listed_status: [],
  super_dimension_patent_category: [],
  expand_status: [],
  ent_scale: [],
  benefit_assess: [],
  register_time: [],
  register_capital: [],
  ent_status: [],
  shit_type: [],
  enttype_data: [],
  all_cert_data: [],
  contact_style: [],
  tel_data: [],
  email_data: [],
  super_dimension_social_num: [],
  super_dimension_biding: [],
  super_dimension_job_info: [],
  tax_credit: [],
  financing_info: [],
  super_dimension_trademark: [],
  super_dimension_patent: [],
  super_dimension_android_app: [],
  super_dimension_ios_app: [],
  super_dimension_mini_app: [],
  super_dimension_wx_extension: [],
  super_dimension_weibo_extension: [],
  untrustworthy_info_data: [],
  judgment_doc_data: [],
  adminstrative_penalties_data: [],
  chattel_mortage_data: [],
  abnormal_operation_data: [],
  software_copyright_data: [],
  work_copyright_data: [],
  super_dimension_website: [],
  super_dimension_icp: [],
  chain_codes_data: [],
  leading_ent: []
};

// PC表单风格的配置数组
const FORM_CONFIG = [
  // 基础筛选分组
  {
    groupTitle: '基础筛选',
    groupKey: 'basic',
    fields: [
      {
        type: 'input',
        label: '企业名称',
        key: 'ent_name',
        placeholder: '请输入企业名称',
        maxLength: 50,
        special: 'input'
      },
      {
        type: 'pop',
        label: '所在地区',
        key: 'areas',
        dataKey: 'regionData',
        popType: 'region',
        special: 'pop'
      },
      {
        type: 'pop',
        label: '所属行业',
        key: 'trade_types',
        dataKey: 'eleseic_data',
        popType: 'industry',
        special: 'pop'
      }
    ]
  },
  // 产业优选分组
  {
    groupTitle: '产业优选',
    groupKey: 'industry',
    fields: [
      {
        type: 'radio',
        label: '龙头企业',
        key: 'leading_ent',
        vip: true,
        options: [
          {value: 'true', label: '是龙头企业'},
          {value: 'false', label: '非龙头企业'}
        ]
      },
      {
        type: 'multiSelect',
        label: '疑似扩张',
        key: 'expand_status',
        vip: true,
        options: [
          {value: 'A', label: '近期融资'},
          {value: 'B', label: '近期新增分支机构'},
          {value: 'C', label: '近期疑似扩张'},
          {value: 'D', label: '近期有对外投资'},
          {value: 'E', label: '近期新增专利公示'},
          {value: 'F', label: '近期新增专利申请'}
        ]
      },
      {
        type: 'pop',
        label: '所属产业链',
        key: 'chain_codes',
        dataKey: 'chain_codes_data',
        popType: 'chain',
        vip: true,
        special: 'pop'
      },
      {
        type: 'select',
        label: '企业规模',
        key: 'ent_scale',
        icon: true,
        vip: true,
        options: [
          {value: '0$500', label: '微型'},
          {value: '500$1000', label: '小微型'},
          {value: '1000$2000', label: '中小型'},
          {value: '2000$10000', label: '中型'},
          {value: '10000$30000', label: '大型'},
          {value: '30000$', label: '特大型'}
        ]
      },
      {
        type: 'multiSelect',
        label: '科技型企业',
        key: 'technology_types',
        vip: true,
        isOpenIcon: true,
        isOpen: true,
        options: [
          {value: 'HN', label: '高新技术企业'},
          {value: 'MST', label: '科技型中小企业'},
          {value: 'PSN', label: '专精特新企业'},
          {value: 'TG', label: '科技小巨人企业'},
          {value: 'G', label: '瞪羚企业'},
          {value: 'U', label: '独角兽企业'},
          {value: 'Z', label: '众创空间'},
          {value: 'E', label: '雏鹰企业'}
        ]
      }
    ]
  },
  // 企业相关分组
  {
    groupTitle: '企业相关',
    groupKey: 'enterprise',
    fields: [
      {
        type: 'datePop',
        label: '注册时间',
        key: 'register_time',
        minLabel: '最低年限',
        maxLabel: '最高年限',
        unit: '年',
        genre: 'tpop',
        options: [
          {value: '0$1', label: '1年内'},
          {value: '1$2', label: '1-2年'},
          {value: '2$3', label: '2-3年'},
          {value: '3$5', label: '3-5年'},
          {value: '5$0', label: '5年以上'}
        ]
      },
      {
        type: 'rangeInput',
        label: '注册资本',
        key: 'register_capital',
        minLabel: '最低资本',
        maxLabel: '最高资本',
        unit: '万元',
        genre: 'input',
        options: [
          {value: '0$100', label: '100万以下'},
          {value: '100$200', label: '100-200万'},
          {value: '500$1000', label: '500-1000万'},
          {value: '1000$5000', label: '1000-5000万'},
          {value: '5000$', label: '5000万以上'}
        ]
      },
      {
        type: 'multiSelect',
        label: '企业状态',
        key: 'ent_status',
        options: [
          {value: 'RUN', label: '在营'},
          {value: 'REVOKE', label: '吊销'},
          {value: 'LOGOUT', label: '注销'},
          {value: 'OTHER', label: '其他'}
        ]
      },
      {
        type: 'pop',
        label: '企业许可',
        key: 'ent_cert',
        dataKey: 'all_cert_data',
        popType: 'cert',
        vip: true,
        special: 'pop'
      },
      {
        type: 'pop',
        label: '企业类型',
        key: 'ent_type',
        dataKey: 'enttype_data',
        popType: 'entType',
        special: 'pop'
      },
      {
        type: 'multiSelect',
        label: '实体类型',
        key: 'shit_type',
        options: [
          {value: 'ENTERPRISE', label: '企业(不包含个体户)'},
          {value: 'INDIVIDUAL', label: '个体户'}
        ]
      }
    ]
  },
  // 经营状态分组
  {
    groupTitle: '经营状态',
    groupKey: 'business',
    fields: [
      {
        type: 'radio',
        label: '联系方式',
        key: 'tel_data',
        vip: true,
        options: [
          {value: 'TEL', label: '有'},
          {value: 'NO_TEL', label: '无'}
        ]
      },
      {
        type: 'radio',
        label: '手机号码',
        key: 'contact_style',
        options: [
          {value: 'PHONE', label: '有'},
          {value: 'NO_PHONE', label: '无'}
        ]
      },
      {
        type: 'radio',
        label: '联系邮箱',
        key: 'email_data',
        options: [
          {value: 'EMAIL', label: '有'},
          {value: 'NO_EMAIL', label: '无'}
        ]
      },
      {
        type: 'rangeInput',
        label: '参保人数',
        key: 'super_dimension_social_num',
        minLabel: '最低人数',
        maxLabel: '最高人数',
        unit: '人',
        genre: 'input',
        options: [
          {value: '0$49', label: '0-49'},
          {value: '50$99', label: '50-99'},
          {value: '100$499', label: '100-499'},
          {value: '500$999', label: '500-999'},
          {value: '1000$4999', label: '1000-4999'},
          {value: '5000$', label: '5000人以上'}
        ]
      },
      {
        type: 'multiSelect',
        label: '融资信息',
        key: 'financing_info',
        vip: true,
        isOpenIcon: true,
        isOpen: true,
        options: [
          {value: '天使轮', label: '天使轮'},
          {value: 'A轮', label: 'A轮'},
          {value: 'B轮', label: 'B轮'},
          {value: 'C轮', label: 'C轮'},
          {value: 'E轮及以上', label: 'E轮及以上'},
          {value: 'IPO', label: 'IPO'},
          {value: '定向增发', label: '定向增发'},
          {value: '战略融资', label: '战略融资'},
          {value: '收购', label: '收购'},
          {value: '其他', label: '其他'}
        ]
      },
      {
        type: 'multiSelect',
        label: '上市状态',
        key: 'listed_status',
        vip: true,
        options: [
          {value: 'LISTED_A', label: 'A股'},
          {value: 'LISTED_B', label: 'B股'},
          {value: 'LISTED_NEW_THIRD_BOARD', label: '新三板'},
          {value: 'LISTED_HK', label: '港股'},
          {value: 'LISTED_TH', label: '科创板'},
          {value: 'LISTED_USA', label: '美股'},
          {value: 'LISTED_NONE', label: '非上市'}
        ]
      },
      {
        type: 'radio',
        label: '招投标',
        key: 'super_dimension_biding',
        vip: true,
        options: [
          {value: 'true', label: '有'},
          {value: 'false', label: '无'}
        ]
      },
      {
        type: 'radio',
        label: '招聘',
        key: 'super_dimension_job_info',
        vip: true,
        options: [
          {value: 'true', label: '有'},
          {value: 'false', label: '无'}
        ]
      },
      {
        type: 'radio',
        label: '纳税信用',
        key: 'tax_credit',
        vip: true,
        options: [
          {value: 'ALEVEL', label: 'A级'},
          {value: 'NONALEVEL', label: '非A级'}
        ]
      }
    ]
  }
];

// 筛选字段配置 - 通过类型分组，便于管理和扩展
const FIELD_CONFIGS = {
  // 输入框类型
  input: {
    ent_name: {
      label: '企业名称',
      placeholder: '请输入企业名称',
      maxLength: 50
    }
  },

  // 单选类型 - 有/无选择
  radio: {
    tel_data: {label: '联系方式'},
    contact_style: {label: '手机号码'},
    email_data: {label: '联系邮箱'},
    super_dimension_biding: {label: '招投标'},
    super_dimension_job_info: {label: '招聘'},
    tax_credit: {label: '纳税信用'},
    super_dimension_trademark: {label: '商标信息'},
    super_dimension_patent: {label: '专利信息'},
    super_dimension_android_app: {label: '安卓APP'},
    super_dimension_ios_app: {label: '苹果APP'},
    super_dimension_mini_app: {label: '小程序'},
    super_dimension_wx_extension: {label: '微信公众号'},
    super_dimension_weibo_extension: {label: '微博'},
    untrustworthy_info_data: {label: '失信信息'},
    judgment_doc_data: {label: '裁判文书'},
    adminstrative_penalties_data: {label: '行政处罚'},
    chattel_mortage_data: {label: '动产抵押'},
    abnormal_operation_data: {label: '经营异常'},
    software_copyright_data: {label: '软件著作权'},
    work_copyright_data: {label: '作品著作权'},
    super_dimension_website: {label: '官网信息'},
    super_dimension_icp: {label: 'ICP备案'},
    leading_ent: {label: '龙头企业'}
  },

  // 多选类型
  multiSelect: {
    capital_event: {label: '资本事件'},
    shit_type: {label: '实体类型'},
    ent_status: {label: '企业状态'},
    technology_types: {label: '科技型企业'},
    financing_info: {label: '融资信息'},
    listed_status: {label: '上市状态'},
    super_dimension_patent_category: {label: '专利内容'},
    expand_status: {label: '疑似扩张'}
  },

  // 范围输入类型
  rangeInput: {
    register_capital: {
      label: '注册资本',
      minLabel: '最低资本',
      maxLabel: '最高资本',
      unit: '万元',
      type: 'number',
      maxLength: 10
    },
    super_dimension_social_num: {
      label: '参保人数',
      minLabel: '最低人数',
      maxLabel: '最高人数',
      unit: '人',
      type: 'number',
      maxLength: 5
    }
  },

  // 日期选择类型
  datePop: {
    register_time: {
      label: '注册时间',
      minLabel: '最低年限',
      maxLabel: '最高年限',
      unit: '年'
    }
  },

  // 弹窗选择类型
  pop: {
    areas: {
      label: '所在地区',
      dataKey: 'regionData',
      popType: 'region'
    },
    trade_types: {
      label: '所属行业',
      dataKey: 'eleseic_data',
      popType: 'industry'
    },
    ent_type: {
      label: '企业类型',
      dataKey: 'enttype_data',
      popType: 'entType'
    },
    ent_cert: {
      label: '企业许可',
      dataKey: 'all_cert_data',
      popType: 'cert'
    },
    chain_codes: {
      label: '所属产业链',
      dataKey: 'chain_codes_data',
      popType: 'chain'
    }
  }
};

// 组件差异配置
const COMPONENT_CONFIGS = {
  hunt: {
    name: '完整搜索组件',
    excludeFields: [],
    excludeFromCategories: {}
  },
  huntCopy: {
    name: '简化搜索组件',
    excludeFields: ['chain_codes'],
    excludeFromCategories: {
      产业优选: ['chain_codes']
    }
  }
};

// 工具函数：获取字段类型
function getFieldType(fieldKey) {
  for (const [type, fields] of Object.entries(FIELD_CONFIGS)) {
    if (fields[fieldKey]) {
      return type;
    }
  }
  return null;
}

// 工具函数：获取字段配置
function getFieldConfig(fieldKey) {
  const type = getFieldType(fieldKey);
  return type ? FIELD_CONFIGS[type][fieldKey] : null;
}

// 工具函数：添加新的筛选字段
function addField(type, key, config) {
  if (!FIELD_CONFIGS[type]) {
    FIELD_CONFIGS[type] = {};
  }
  FIELD_CONFIGS[type][key] = config;

  // 同时添加到默认参数中
  if (!DEFAULT_PARAMS.hasOwnProperty(key)) {
    DEFAULT_PARAMS[key] = Array.isArray(config.defaultValue) ? [] : '';
  }
}

// 工具函数：移除筛选字段
function removeField(fieldKey) {
  const type = getFieldType(fieldKey);
  if (type && FIELD_CONFIGS[type][fieldKey]) {
    delete FIELD_CONFIGS[type][fieldKey];
    delete DEFAULT_PARAMS[fieldKey];
  }
}

// 工具函数：获取组件配置
function getComponentConfig(componentType) {
  return COMPONENT_CONFIGS[componentType] || COMPONENT_CONFIGS.hunt;
}

// 工具函数：过滤字段（根据组件类型）
function filterFields(componentType) {
  const config = getComponentConfig(componentType);
  const filteredFields = {};

  Object.keys(FIELD_CONFIGS).forEach(type => {
    filteredFields[type] = {};
    Object.keys(FIELD_CONFIGS[type]).forEach(key => {
      // 检查是否在排除列表中
      if (!config.excludeFields.includes(key)) {
        filteredFields[type][key] = FIELD_CONFIGS[type][key];
      }
    });
  });

  return filteredFields;
}

// 布尔类型字段列表（用于数据处理）
const BOOLEAN_FIELDS = [
  'super_dimension_biding',
  'super_dimension_job_info',
  'super_dimension_trademark',
  'super_dimension_patent',
  'super_dimension_android_app',
  'super_dimension_ios_app',
  'super_dimension_mini_app',
  'super_dimension_wx_extension',
  'super_dimension_weibo_extension',
  'super_dimension_website',
  'super_dimension_icp',
  'leading_ent'
];

// 父级类型前缀（用于数据处理）
const PARENT_TYPE_PREFIXES = ['super_dimension_'];

module.exports = {
  DEFAULT_PARAMS,
  FIELD_CONFIGS,
  COMPONENT_CONFIGS,
  BOOLEAN_FIELDS,
  PARENT_TYPE_PREFIXES,

  // 工具函数
  getFieldType,
  getFieldConfig,
  addField,
  removeField,
  getComponentConfig,
  filterFields
};
