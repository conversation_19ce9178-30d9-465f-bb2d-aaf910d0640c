<view class="form-multi-select">
  <!-- 字段标题 -->
  <view class="field-header">
    <view class="field-title">
      <text class="title-text">{{field.label}}</text>
      <view wx:if="{{field.required}}" class="required-mark">*</view>
      <view wx:if="{{field.vip}}" class="vip-badge">VIP</view>
      <view wx:if="{{field.icon}}" class="info-icon" bindtap="onInfoClick">?</view>
    </view>
    
    <!-- 展开/收起按钮 -->
    <view wx:if="{{field.isOpenIcon}}" 
          class="expand-btn" 
          bindtap="toggleExpand">
      <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/h-upward.png" 
             mode="aspectFill" 
             wx:if="{{!isExpanded}}"></image>
      <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/h-down.png" 
             mode="aspectFill" 
             wx:else></image>
    </view>
  </view>

  <!-- 选项列表 -->
  <view class="options-container" 
        style="height: {{field.isOpenIcon && isExpanded ? '174rpx' : 'auto'}};">
    
    <!-- 选中状态显示 -->
    <view wx:if="{{!isExpanded && selectedCount > 0}}" class="selected-summary">
      <text class="summary-text">{{getSelectedText()}}</text>
    </view>

    <!-- 选项列表 -->
    <view wx:if="{{isExpanded}}" class="options-list">
      <view wx:for="{{field.options}}" 
            wx:key="value" 
            wx:for-item="option"
            class="option-item {{option.active ? 'active' : ''}}"
            bindtap="onOptionSelect"
            data-option="{{option}}">
        <text class="option-text">{{option.label}}</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view wx:if="{{isExpanded && field.showActions}}" class="action-buttons">
      <view class="action-btn" bindtap="selectAll">全选</view>
      <view class="action-btn" bindtap="clearAll">清空</view>
    </view>
  </view>

  <!-- 选择统计 -->
  <view wx:if="{{selectedCount > 0}}" class="selection-stats">
    <text class="stats-text">已选择 {{selectedCount}} 项</text>
    <text wx:if="{{field.maxSelect}}" class="stats-limit">/ 最多{{field.maxSelect}}项</text>
  </view>

  <!-- 错误提示 -->
  <view wx:if="{{field.error}}" class="error-message">
    <text class="error-text">{{field.error}}</text>
  </view>
</view>
